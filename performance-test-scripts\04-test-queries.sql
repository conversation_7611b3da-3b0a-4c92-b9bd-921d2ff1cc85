-- Performance Testing Queries
-- Use these queries to test and benchmark the getRangeGroupedStats performance

\timing on

-- Test 1: Current ORM-style query (simulating the problematic approach)
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
SELECT 
    es.*,
    a.customer_id,
    a.threshold_type,
    a.description as alert_description,
    ast.a_type,
    m.m_type
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
JOIN asset ast ON a.asset_id = ast.id
JOIN measurement m ON a.measurement_id = m.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL
ORDER BY es.start_time;

-- Test 2: Optimized aggregation query (recommended approach)
EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
SELECT 
    COUNT(*) as total_excursions,
    COALESCE(SUM(EXTRACT(EPOCH FROM es.time_duration)), 0) as total_duration_seconds,
    
    -- Threshold type counts
    COUNT(*) FILTER (WHERE att.name = 'High Alarm') as high_alarm_count,
    COUNT(*) FILTER (WHERE att.name = 'Low Alarm') as low_alarm_count,
    COUNT(*) FILTER (WHERE att.name = 'High Warning') as high_warning_count,
    COUNT(*) FILTER (WHERE att.name = 'Low Warning') as low_warning_count,
    COUNT(*) FILTER (WHERE att.name = 'Deviation') as deviation_count,
    
    -- Asset type counts
    COUNT(*) FILTER (WHERE at.name = 'Pump') as pump_count,
    COUNT(*) FILTER (WHERE at.name = 'Motor') as motor_count,
    COUNT(*) FILTER (WHERE at.name = 'Sensor') as sensor_count,
    COUNT(*) FILTER (WHERE at.name = 'Valve') as valve_count,
    COUNT(*) FILTER (WHERE at.name = 'Tank') as tank_count,
    
    -- Measurement type counts
    COUNT(*) FILTER (WHERE mt.name = 'Temperature') as temperature_count,
    COUNT(*) FILTER (WHERE mt.name = 'Pressure') as pressure_count,
    COUNT(*) FILTER (WHERE mt.name = 'Flow Rate') as flow_rate_count,
    COUNT(*) FILTER (WHERE mt.name = 'Vibration') as vibration_count,
    COUNT(*) FILTER (WHERE mt.name = 'Level') as level_count
    
FROM excursion_stats es
INNER JOIN alerts a ON es.alert_id = a.id
INNER JOIN asset ast ON a.asset_id = ast.id  
INNER JOIN asset_type at ON ast.a_type = at.id
INNER JOIN measurement m ON a.measurement_id = m.id
INNER JOIN measurement_type mt ON m.m_type = mt.id
LEFT JOIN alert_threshold_type att ON a.threshold_type = att.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Test 3: Different time ranges to test scalability
-- Last 7 days
SELECT 'Last 7 days test' as test_name;
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) 
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '7 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Last 90 days
SELECT 'Last 90 days test' as test_name;
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) 
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '90 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Test 4: Index usage verification
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename IN ('excursion_stats', 'alerts')
ORDER BY idx_scan DESC;

-- Test 5: Query plan comparison for different customer IDs
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*) 
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
WHERE a.customer_id = 2  -- Different customer
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Test 6: Memory and I/O statistics
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%excursion_stats%'
ORDER BY total_time DESC
LIMIT 10;

-- Test 7: Table and index statistics
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE tablename IN ('excursion_stats', 'alerts', 'asset', 'measurement')
ORDER BY n_live_tup DESC;
