import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import authConfiguration from "src/authentication/auth.config";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { User } from "src/users/domain/user.entity";
import { CsrfGuard } from "../authentication/infra/csrf.guard";
import { JwtAuthGuard } from "../authentication/infra/jwt-auth.guard";
import { Role } from "../authorization/domain/customer-user-role.entity";
import { HasRole } from "../authorization/infra/roles.decorator";
import { RolesGuard } from "../authorization/infra/roles.guard";
import { AlertService } from "./alert.service";
import { AlertDTO } from "./dto/alert.dto";

@Controller({
  path: "alert",
  version: "0",
})
@UseGuards(JwtAuthGuard, CsrfGuard)
export class AlertController {
  constructor(
    private readonly alertService: AlertService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Get("/")
  @HasRole(Role.USER)
  @HttpCode(200)
  async getAlerts(@CookieToken() headers: Request["headers"]) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.alertService.getAlerts({ customerId });
  }

  @Get("/details/:id")
  @HasRole(Role.USER)
  async getAlert(@Param("id") id: number) {
    return await this.alertService.getAlert(id);
  }

  @Get("/aggregates")
  @HasRole(Role.USER)
  async getAggregates() {
    return await this.alertService.getAggregates();
  }

  @Get("/thresholdTypes")
  @HasRole(Role.USER)
  async getThresholdTypes() {
    return await this.alertService.getThresholdTypes();
  }

  @Get("/conditions")
  @HasRole(Role.USER)
  async getAlertConditions() {
    return await this.alertService.getAlertConditions();
  }

  @Post("/")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.POWER_USER)
  @HttpCode(200)
  async createAlert(
    @Body() alert: AlertDTO,
    @CookieToken() headers: Request["headers"],
    @Req() req: any
  ) {
    const createdAlert = await this.alertService.createAlert(alert, headers, req.user);
    return {
      message: "Alert Created Successfully",
      data: createdAlert
    };
  }

  @Put("/enable/:id")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.POWER_USER)
  @HttpCode(201)
  async enableAlert(
    @Param("id") id: number,
    @CookieToken() headers: Request["headers"]
  ) {
    return await this.alertService.enableAlert(id, headers);
  }

  @Put("/:id")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.POWER_USER)
  @HttpCode(201)
  async updateAlert(
    @Param("id") id: number,
    @Body() alert: AlertDTO,
    @CookieToken() headers: Request["headers"],
    @Req() req: any
  ) {
    return await this.alertService.updateAlert(id, alert, headers, req.user);
  }

  @Delete("/:id")
  @UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
  @HasRole(Role.POWER_USER)
  @HttpCode(204)
  async deleteAlert(
    @Param("id") id: number,
    @CookieToken() headers: Request["headers"],
    @AuthUser() authUser: User
  ) {
    return await this.alertService.deleteAlert(id, headers, authUser);
  }

  @Get("/measurements/:id")
  @HasRole(Role.USER)
  async getMeasurements(@Param("id") id: number) {
    return await this.alertService.getMeasurementAlerts(id);
  }

  @Get("/periods")
  @HasRole(Role.USER)
  async getPeriods() {
    return await this.alertService.getPeriods();
  }

  @Get("/events")
  @HasRole(Role.USER)
  async getAllEvents(
    @CookieToken() headers: Request["headers"],
    @Query("start") start: string,
    @Query("end") end: string
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.alertService.getAllEvents({
      customerId,
      start: Number(start),
      end: Number(end),
    });
  }

  @Get("/events/:id")
  @HasRole(Role.USER)
  async getEvents(@Param("id") id: number) {
    return await this.alertService.getEvents(id);
  }

  @Get("/event/:alertId")
  @HasRole(Role.USER)
  async getEvent(@Param("alertId") alertId: number) {
    return await this.alertService.getEventsByAlertId(alertId);
  }
}
