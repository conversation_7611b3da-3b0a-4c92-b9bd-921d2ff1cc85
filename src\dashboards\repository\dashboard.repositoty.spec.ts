import { DashboardRepository } from "./dashboard.repository";

// Mock the Reference.createFromPK method
jest.mock("@mikro-orm/core", () => ({
  ...jest.requireActual("@mikro-orm/core"),
  Reference: {
    createFromPK: jest.fn((Entity, id) => ({ id })),
  },
}));
describe("DashboardRepository", () => {
  let repository: DashboardRepository;
  let mockLogger: any;
  let mockEntityRepo: any;
  let mockDefaultDashboardRepo: any;
  let mockFavoriteDashboardRepo: any;
  let mockCustomerDefaultDashboardRepo: any;
  let mockCustomerFavoriteDashboardRepo: any;
  let mockAssetRepo: any;
  let mockDashboardTemplateRepo: any;
  let mockMeasurementRepo: any;
  let mockAssetMeasurementRepo: any;
  let mockAssetRepository: any;
  let mockAssetMeasurementService: any;
  let mockTransactionFactory: any;

  beforeEach(() => {
    mockLogger = { log: jest.fn(), error: jest.fn() };
    mockEntityRepo = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      persistAndFlush: jest.fn(),
      getEntityManager: jest.fn(() => ({
        persistAndFlush: jest.fn(),
        flush: jest.fn(),
        getRepository: jest.fn(() => ({ find: jest.fn() })),
      })),
    };
    mockDefaultDashboardRepo = {
      findOne: jest.fn(),
      nativeUpdate: jest.fn(),
      create: jest.fn(),
      persistAndFlush: jest.fn(),
      nativeDelete: jest.fn(),
    };
    mockFavoriteDashboardRepo = {
      find: jest.fn(),
      findOne: jest.fn(),
      nativeInsert: jest.fn(),
      nativeDelete: jest.fn(),
    };
    mockCustomerDefaultDashboardRepo = { findOne: jest.fn() };
    mockCustomerFavoriteDashboardRepo = { find: jest.fn() };
    mockAssetRepo = { findOne: jest.fn() };
    mockDashboardTemplateRepo = { findOne: jest.fn() };
    mockMeasurementRepo = { findOne: jest.fn() };
    mockAssetMeasurementRepo = { findOne: jest.fn() };
    mockAssetRepository = { findById: jest.fn() };
    mockAssetMeasurementService = { getAll: jest.fn() };
    mockTransactionFactory = { run: jest.fn((cb) => cb()) };

    repository = new DashboardRepository(
      mockLogger,
      mockEntityRepo,
      mockDefaultDashboardRepo,
      mockFavoriteDashboardRepo,
      mockCustomerDefaultDashboardRepo,
      mockCustomerFavoriteDashboardRepo,
      mockAssetRepo,
      mockDashboardTemplateRepo,
      mockMeasurementRepo,
      mockAssetMeasurementRepo,
      mockAssetRepository,
      mockAssetMeasurementService,
      mockTransactionFactory
    );
  });

  describe("update", () => {
    it("should throw if dashboard not found", async () => {
      repository.findById = jest.fn().mockResolvedValue(null);
      await expect(
        repository.update({ title: "New", data: "" }, 2, 1, 1)
      ).rejects.toThrow();
    });
  });

  describe("findById", () => {
    it("should return dashboard if found", async () => {
      mockEntityRepo.findOne.mockResolvedValue({ id: 1 });
      const result = await repository.findById(1);
      expect(result).toEqual({ id: 1 });
    });
    it("should return null if not found", async () => {
      mockEntityRepo.findOne.mockResolvedValue(null);
      const result = await repository.findById(1);
      expect(result).toBeNull();
    });
  });

  describe("capitalizeFirstLetter", () => {
    it("should capitalize first letter", () => {
      expect(repository.capitalizeFirstLetter("test")).toBe("Test");
    });
  });

  describe("formatMetricLabel", () => {
    it("should format metric label", () => {
      expect(repository.formatMetricLabel("foo_bar")).toBe("Foo Bar");
      expect(repository.formatMetricLabel("foo\\bar_baz")).toBe("Bar Baz");
    });
    it("should return empty string for falsy", () => {
      expect(repository.formatMetricLabel("")).toBe("");
    });
  });

  describe("delete", () => {
    it("should throw if dashboard not found", async () => {
      repository.findById = jest.fn().mockResolvedValue(null);
      await expect(repository.delete(1, 2)).rejects.toThrow();
    });
  });

  describe("defaultDashboard", () => {
    it("should throw if already default", async () => {
      mockDefaultDashboardRepo.findOne.mockResolvedValue({
        dashboard: { id: 1 },
      });
      await expect(
        repository.defaultDashboard(1, 2, 3, true)
      ).rejects.toThrow();
    });
  });

  describe("findAssetByMeasurement", () => {
    it("should find asset by measurement", async () => {
      mockAssetMeasurementRepo.findOne.mockResolvedValue({ id: 1 });
      const result = await repository.findAssetByMeasurement(1, 2);
      expect(result).toEqual({ id: 1 });
    });
  });
  describe("findByIdWithDefault", () => {
    it("should find dashboard with default status", async () => {
      const mockDashboard = { 
        id: 1, 
        title: "Test",
        customer: { id: 1 },
        asset: null,
        dashboardTemplate: null
      };
      repository.findById = jest.fn().mockResolvedValue(mockDashboard);
      mockEntityRepo.findOne.mockResolvedValue(mockDashboard);
      mockDefaultDashboardRepo.findOne.mockResolvedValue(null);

      const result = await repository.findByIdWithDefault(1, 1, 2);

      expect(result).toHaveProperty("default");
      expect(result.default).toBe(false);
    });
  });

  describe("add", () => {
    it("should add new dashboard", async () => {
      const dashboardData = { title: "New Dashboard", data: "{}" };
      mockTransactionFactory.run.mockImplementation((cb) => cb(mockEntityRepo));
      mockEntityRepo.create.mockReturnValue({ id: 1 });
      mockEntityRepo.persistAndFlush.mockResolvedValue(undefined);

      const result = await repository.add(dashboardData, 1, 1);

      expect(mockEntityRepo.create).toHaveBeenCalled();
      expect(mockEntityRepo.persistAndFlush).toHaveBeenCalled();
    });
  });

  describe("findFavoriteDashboard", () => {
    it("should find favorite dashboard", async () => {
      const mockFavorite = { id: 1, dashboard: { id: 1 } };
      mockFavoriteDashboardRepo.findOne.mockResolvedValue(mockFavorite);

      const result = await repository.findFavoriteDashboard(1, 2, 3);

      expect(result).toEqual(mockFavorite);
      expect(mockFavoriteDashboardRepo.findOne).toHaveBeenCalled();
    });
  });

  describe("createFavoriteDashboard", () => {
    it("should create favorite dashboard", async () => {
      mockFavoriteDashboardRepo.nativeInsert.mockResolvedValue(1);

      const result = await repository.createFavoriteDashboard(1, 2, 3);

      expect(result).toBe(1);
      expect(mockFavoriteDashboardRepo.nativeInsert).toHaveBeenCalled();
    });
  });

  describe("removeFavoriteDashboard", () => {
    it("should remove favorite dashboard", async () => {
      mockFavoriteDashboardRepo.nativeDelete.mockResolvedValue(1);

      const result = await repository.removeFavoriteDashboard(1, 2, 3);

      expect(result).toBe(1);
      expect(mockFavoriteDashboardRepo.nativeDelete).toHaveBeenCalled();
    });
  });
});
