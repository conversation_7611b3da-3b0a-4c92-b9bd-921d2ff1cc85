import { Injectable } from "@nestjs/common";
import { AlertStatsRepository } from "./repository/alert-stats.repository";

@Injectable()
export class AlertStatsService {
  constructor(private readonly alertStatsRepository: AlertStatsRepository) {}

  async getAlertStatsById({ alert_id }: { alert_id: number }) {
    return await this.alertStatsRepository.getStatByAlert({ id: alert_id });
  }

  async getAllExcursionStats() {
    const excursionStats =
      await this.alertStatsRepository.getAllExcursionStats();
    return excursionStats.map((excursion) => {
      const measureTag = excursion.alert?.measurement?._tag ?? null;
      return {
        ...excursion,
        measureTag,
      };
    });
  }

  async getGroupedStats(
    customerId: number,
    start: number,
    end: number,
    interval?: "daily" | "weekly" | "monthly",
    assetId?: number,
    measureId?: number,
    date?: string
  ) {
    const stats = await this.alertStatsRepository.getGroupedStats(
      customerId,
      start,
      end,
      interval,
      assetId,
      measureId,
      date
    );
    const statsWithDescription = stats.map((item: any) => {
      let endTime = item.end_time;

      if (!endTime && item.period_start && item.total_duration_seconds) {
        const start = new Date(item.period_start);
        if (!isNaN(start.getTime())) {
          endTime = new Date(
            start.getTime() + Number(item.total_duration_seconds) * 1000
          ).toISOString();
        }
      }
      return {
        ...item,
        measureTag: item.tag ?? null,
        description: item.description ?? null,
      };
    });
    return statsWithDescription;
  }
  async getRangeGroupedStats(customer: number, start?: number, end?: number) {
    return await this.alertStatsRepository.getRangeGroupedStats(
      customer,
      start,
      end
    );
  }
}
