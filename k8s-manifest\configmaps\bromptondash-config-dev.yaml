apiVersion: v1
kind: ConfigMap
metadata:
  name: admin-api-dev-config
  namespace: application
data:
  NODE_ENV: "dev"
  SERVER_PORT: "8081"
  
  # Database Configuration
  DB_HOST: "dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com"
  DB_NAME: "dataloggertest"
  DB_PORT: "5432"
  DB_USER: "postgres"
  DB_SSL: "false"
  
  # Frontend URL
  FRONTEND_URL: "https://dev.pivotol.ai"
  
  # API Configuration
  FAST_API: "http://timeseries-api-service.application.svc.cluster.local"
  TS_API_HOST: "timeseries-api-service.application.svc.cluster.local"
  TS_API_PORT: "443"
  TS_API_SSL: "false"
  TS_API_VERSION: "v1_0"
  
  # MQTT Configuration
  MQTT_URL: "mqtt://bromptonenergy.io:8883"
  MQTT_USERNAME: ""
  MQTT_TOPIC_ALERT: "spBv1.0/Brenes_Gateway/NDATA/#"
  MQTT_TOPIC_NOTIFICATION: "be-notification-dev"
  MQTT_CLIENT_ID: "beasdf"
  MQTT_ROLE: "publisher"
  
  # Notification Configuration
  SEND_NOTIFICATIONS: "true"
  SEND_SMS_NOTIFICATIONS: "false"
  SMS_FROM_NO: "+13854427050"
  EMAIL_FROM: "<EMAIL>"
  
  # Scheduler Configuration
  SCHEDULER_ENDPOINT: "http://scheduler-api-dev.application.svc.cluster.local/"
  
  # AWS Configuration
  REGION: "us-east-1"
  
  # Kafka Configuration
  KAFKA_BROKER: "strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092"
  KAFKA_CLIENT_ID: "nestjs-client"
  KAFKA_TOPIC_ALERT: "be-alert-dev-new"
  KAFKA_TOPIC_NOTIFICATION: "be-notification-dev-new"
  KAFKA_GROUP_ID: "nestjs-consumer"
  KAFKA_GROUP_ID_INSTANCE: "nestjs-consumer-instance"
  KAFKA_NOTIFICATION_GROUP_ID: "notification-consumer"
  KAFKA_NOTIFICATIONP_ID_INSTANCE: "notification-consumer-instance"
  
  # Security Configuration
  SECURITY_CORS_ORIGIN_URL: "https://dev.pivotol.ai"
  X_ACTIVE_CUSTOMER: "x-active-customer-id"
  AUTH_COOKIE_DOMAIN: "dev.pivotol.ai"
  AUTH_SESSION_DURATION_MIN: "43200"
  
  # Weather API Configuration
  TEMPEST_WEATHER_API_URL: "https://swd.weatherflow.com/swd/rest"
  TEMPEST_WEATHER_API_TOKEN: "16f1648e-fd77-4fd4-ba09-b711e7de8733"
  
  # TLS Configuration
  TEMPEST_WEATHER_API_URL: "https://swd.weatherflow.com/swd/rest"

  # RABBITMQ Config
  RABBITMQ_BROKER: 'bromptonenergy.io:8884'
  RABBITMQ_USERNAME: 'mosquitto'
  RABBIT_MQ_TOPIC_EXCHANGE: 'topicExchange'
  RABBIT_MQ_ALERT_QUEUE_NAME: 'alert.consumer.queue'
  RABBIT_MQ_NOTIFICATION_QUEUE_NAME: 'notification.consumer.queue'
