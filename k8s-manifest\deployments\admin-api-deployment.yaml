apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-api-${ENVIRONMENT}
  namespace: application
  labels:
    app.kubernetes.io/instance: admin-api-${ENVIRONMENT}
    app.kubernetes.io/name: admin-api-${ENVIRONMENT}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/instance: admin-api-${ENVIRONMENT}
      app.kubernetes.io/name: admin-api-${ENVIRONMENT}
  replicas: 1
  minReadySeconds: 10
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: admin-api-${ENVIRONMENT}
        app.kubernetes.io/name: admin-api-${ENVIRONMENT}
      annotations:
        instrumentation.opentelemetry.io/inject-nodejs: "openobserve-collector/openobserve-nodejs"
    spec:
      terminationGracePeriodSeconds: 10
      containers:
      - name: admin-api-${ENVIRONMENT}
        image: 067172429169.dkr.ecr.us-east-1.amazonaws.com/brompton-energy/backend-${ENVIRONMENT}:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 8081
          name: admin-api-port
        livenessProbe:
          httpGet:
            path: /health/liveness
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/readiness
            port: 8081
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 20
          successThreshold: 1
          failureThreshold: 3
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: NODE_ENV
        - name: SERVER_PORT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SERVER_PORT
        # Database Configuration
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: DB_HOST
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: DB_NAME
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: DB_PASSWORD
        - name: RABBIT_MQ_PASSWORD
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: RABBIT_MQ_PASSWORD      
        - name: DB_SSL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: DB_SSL
        - name: RABBITMQ_BROKER
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: RABBITMQ_BROKER     
        - name: RABBITMQ_USERNAME
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: RABBITMQ_USERNAME   
        - name: RABBIT_MQ_TOPIC_EXCHANGE
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: RABBIT_MQ_TOPIC_EXCHANGE  
        - name: RABBIT_MQ_ALERT_QUEUE_NAME
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: RABBIT_MQ_ALERT_QUEUE_NAME   
        - name: RABBIT_MQ_NOTIFICATION_QUEUE_NAME
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: RABBIT_MQ_NOTIFICATION_QUEUE_NAME                 
        # Frontend URL
        - name: FRONTEND_URL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: FRONTEND_URL
        # API Configuration
        - name: FAST_API
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: FAST_API
        - name: TS_API_HOST
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TS_API_HOST
        - name: TS_API_PORT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TS_API_PORT
        - name: TS_API_SSL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TS_API_SSL
        - name: TS_API_VERSION
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TS_API_VERSION
        - name: SCHEDULER_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SCHEDULER_ENDPOINT
        - name: SMS_FROM_NO
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SMS_FROM_NO
        - name: EMAIL_FROM
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: EMAIL_FROM            
        # MQTT Configuration
        - name: MQTT_URL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_URL
        - name: MQTT_USERNAME
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_USERNAME
        - name: MQTT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: MQTT_PASSWORD
              optional: true
        - name: REGION
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: REGION
        - name: MQTT_TOPIC_ALERT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_TOPIC_ALERT
        - name: MQTT_TOPIC_NOTIFICATION
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_TOPIC_NOTIFICATION
        - name: MQTT_CLIENT_ID
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_CLIENT_ID
        - name: MQTT_ROLE
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: MQTT_ROLE
        - name: TEMPEST_WEATHER_API_TOKEN
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TEMPEST_WEATHER_API_TOKEN   
        - name: TEMPEST_WEATHER_API_URL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: TEMPEST_WEATHER_API_URL                
        # AWS Credentials
        - name: ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: ACCESS_KEY_ID
        - name: SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: SECRET_ACCESS_KEY
        # Twilio Credentials
        - name: TWILLIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: TWILLIO_ACCOUNT_SID
        - name: TWILLIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: TWILLIO_AUTH_TOKEN
        - name: TWILLIO_EMAIL_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: TWILLIO_EMAIL_API_TOKEN
        # Other configurations from ConfigMap
        - name: SECURITY_CORS_ORIGIN_URL
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SECURITY_CORS_ORIGIN_URL
        - name: SEND_NOTIFICATIONS
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SEND_NOTIFICATIONS
        - name: SEND_SMS_NOTIFICATIONS
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: SEND_SMS_NOTIFICATIONS
        - name: X_ACTIVE_CUSTOMER
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: X_ACTIVE_CUSTOMER
        - name: AUTH_COOKIE_DOMAIN
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: AUTH_COOKIE_DOMAIN
        - name: AUTH_SESSION_DURATION_MIN
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: AUTH_SESSION_DURATION_MIN      
        - name: AUTH_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: admin-api-${ENVIRONMENT}-secrets
              key: AUTH_JWT_SECRET
        # Kafka Configuration
        - name: KAFKA_BROKER
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_BROKER
        - name: KAFKA_CLIENT_ID
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_CLIENT_ID
        - name: KAFKA_TOPIC_ALERT
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_TOPIC_ALERT
        - name: KAFKA_TOPIC_NOTIFICATION
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_TOPIC_NOTIFICATION
        - name: KAFKA_GROUP_ID
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_GROUP_ID
        - name: KAFKA_GROUP_ID_INSTANCE
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_GROUP_ID_INSTANCE
        - name: KAFKA_NOTIFICATION_GROUP_ID
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_NOTIFICATION_GROUP_ID
        - name: KAFKA_NOTIFICATIONP_ID_INSTANCE
          valueFrom:
            configMapKeyRef:
              name: admin-api-${ENVIRONMENT}-config
              key: KAFKA_NOTIFICATIONP_ID_INSTANCE
        resources:
          limits:
            cpu: 500m
            memory: 1500Mi
          requests:
            cpu: 100m
            memory: 500Mi