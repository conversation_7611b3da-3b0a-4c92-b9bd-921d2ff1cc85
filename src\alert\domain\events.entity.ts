import { <PERSON><PERSON>ty, ManyToOne, Primary<PERSON>ey, Property } from "@mikro-orm/core";
import { Asset } from "src/assets/domain/asset.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { Aggregates } from "./aggregates.entity";
import { AlertCondition } from "./alertCondition.entity";
import { Periods } from "./periods.entity";
import { Alerts } from "./alert.entity";

@Entity()
export class Events {
  @PrimaryKey()
  id!: number;

  @Property()
  timestamp!: Date;

  @Property()
  deadband!: number;

  @Property()
  state!: number;

  @Property()
  limit!: number;

  @Property()
  input_value!: number;

  @ManyToOne({ entity: () => AlertCondition, fieldName: "condition" })
  comparator!: AlertCondition;

  @ManyToOne({ entity: () => Aggregates, fieldName: "aggregate" })
  aggregate!: Aggregates;

  @ManyToOne({ entity: () => Periods, fieldName: "period", nullable: true})
  period!: Periods;

  @ManyToOne({ entity: () => Asset, fieldName: "asset" })
  asset_id!: Asset;

  @ManyToOne({
    entity: () => Measurement,
    fieldName: "measurement",
  })
  measurement_id!: Measurement;

  @ManyToOne({ entity: () => Alerts, fieldName: "alert_id" })
  alert_id!: Alerts;
}
