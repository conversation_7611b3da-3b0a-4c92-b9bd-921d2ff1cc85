-- Performance Test Database Schema Setup
-- This script creates the minimal schema needed for testing getRangeGroupedStats performance

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Create basic lookup tables
CREATE TABLE IF NOT EXISTS "user" (
    "id" SERIAL PRIMARY KEY,
    "email" VARCHAR(255) NOT NULL,
    "name" VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS "customer" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(100) NOT NULL
);

CREATE TABLE IF NOT EXISTS "asset_type" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL,
    "parent_type" INT NULL,
    "lower_case_name" VARCHAR(50) GENERATED ALWAYS AS (LOWER("name")) STORED
);

CREATE TABLE IF NOT EXISTS "measurement_type" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS "data_type" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS "alert_threshold_type" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS "aggregates" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS "periods" (
    "id" SERIAL PRIMARY KEY,
    "name" VARCHAR(50) NOT NULL
);

-- Create main tables
CREATE TABLE IF NOT EXISTS "asset" (
    "id" SERIAL PRIMARY KEY,
    "tag" VARCHAR(50) NOT NULL,
    "enabled" BOOLEAN DEFAULT true,
    "latitude" FLOAT4 NULL,
    "longitude" FLOAT4 NULL,
    "a_type" INT NOT NULL,
    "customer" INT NOT NULL,
    "is_cust_primary" BOOLEAN NULL,
    "description" VARCHAR(100) NULL,
    "timezone" TEXT NULL,
    "created" TIMESTAMPTZ(6) NULL,
    "updated" TIMESTAMPTZ(6) NULL,
    "createdby" INT NULL,
    "updatedby" INT NULL,
    CONSTRAINT "type_fk" FOREIGN KEY ("a_type") REFERENCES "asset_type" ("id"),
    CONSTRAINT "customer_fk" FOREIGN KEY ("customer") REFERENCES "customer" ("id")
);

CREATE TABLE IF NOT EXISTS "measurement" (
    "id" SERIAL PRIMARY KEY,
    "tag" VARCHAR(150) NOT NULL,
    "enabled" BOOLEAN DEFAULT true,
    "created" TIMESTAMPTZ(6) NULL,
    "updated" TIMESTAMPTZ(6) NULL,
    "createdby" INT NULL,
    "updatedby" INT NULL,
    "m_type" INT NOT NULL,
    "data_type" INT NOT NULL,
    "description" VARCHAR(100) NULL,
    CONSTRAINT "measurement_type_fk" FOREIGN KEY ("m_type") REFERENCES "measurement_type" ("id"),
    CONSTRAINT "measurement_data_type_fk" FOREIGN KEY ("data_type") REFERENCES "data_type" ("id")
);

CREATE TABLE IF NOT EXISTS "alerts" (
    "id" SERIAL PRIMARY KEY,
    "asset_id" INT NOT NULL,
    "measurement_id" INT NOT NULL,
    "agg" INT NOT NULL,
    "period" INT NULL,
    "threshold_type" INT NOT NULL,
    "threshold_value" REAL NULL,
    "reset_deadband" REAL NULL,
    "stale_band" REAL NULL,
    "description" VARCHAR(150) NULL,
    "notification_type" INT NULL,
    "customer_id" INT NULL,
    "enabled" BOOLEAN DEFAULT true,
    "state" INT NULL,
    "created_at" TIMESTAMPTZ(6) NULL,
    "updated_at" TIMESTAMPTZ(6) NULL,
    "deleted_at" TIMESTAMPTZ(6) NULL,
    "deleted_by" INT NULL,
    "last_processed_ts" BIGINT NULL,
    CONSTRAINT "alerts_asset_fk" FOREIGN KEY ("asset_id") REFERENCES "asset" ("id"),
    CONSTRAINT "alerts_measurement_fk" FOREIGN KEY ("measurement_id") REFERENCES "measurement" ("id"),
    CONSTRAINT "alerts_agg_fk" FOREIGN KEY ("agg") REFERENCES "aggregates" ("id"),
    CONSTRAINT "alerts_period_fk" FOREIGN KEY ("period") REFERENCES "periods" ("id"),
    CONSTRAINT "alerts_threshold_type_fk" FOREIGN KEY ("threshold_type") REFERENCES "alert_threshold_type" ("id"),
    CONSTRAINT "alerts_customer_fk" FOREIGN KEY ("customer_id") REFERENCES "customer" ("id"),
    CONSTRAINT "alerts_deleted_by_fk" FOREIGN KEY ("deleted_by") REFERENCES "user" ("id")
);

CREATE TABLE IF NOT EXISTS "excursion_stats" (
    "id" SERIAL PRIMARY KEY,
    "alert_id" INT NOT NULL,
    "start_time" TIMESTAMP NOT NULL,
    "end_time" TIMESTAMP NOT NULL,
    "duration" VARCHAR(255) NOT NULL,
    "max_value" DOUBLE PRECISION,
    "min_value" DOUBLE PRECISION,
    "avg_value" DOUBLE PRECISION,
    "time_duration" INTERVAL,
    CONSTRAINT "fk_excursion_alert" FOREIGN KEY ("alert_id") REFERENCES "alerts"("id") ON DELETE CASCADE
);

-- Create existing indexes (from current schema)
CREATE INDEX IF NOT EXISTS "asset_a_type_idx" ON "asset" ("a_type");
CREATE INDEX IF NOT EXISTS "measurement_m_type_idx" ON "measurement" ("m_type");
CREATE INDEX IF NOT EXISTS "measurement_data_type_idx" ON "measurement" ("data_type");

-- Note: excursion_stats has NO indexes initially to simulate current production state
-- Performance indexes will be added in separate test scripts
