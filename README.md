## Installation

```bash
$ yarn install
```

## Run migrations

```bash
# development
$ yarn run migrations:up:dev
```

## Running the app

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Cloning database

1. Dump roles

```bash
pg_dumpall -r -h <IP_ADDRESS> -p 5432 -U postgres -f db_roles_dump
```

2. Dump schema and data

```bash
pg_dump -v -Fc -C -d DataLogger -h <IP_ADDRESS> -p 5432 -U postgres > db_dump
```

3. Restore roles

```bash
cat db_roles_dump | psql -h localhost -U postgres
```

4. Restore database

```bash
pg_restore -C -h localhost -p 5432 -U postgres -f - db_dump | psql -h localhost -U postgres
```

## database backup from production to local database

```
pg_dump -h <database_url> -p <port> -U postgres -d <databasename> -W -F c -b -v -f <file_name>.dump
```

- this will create new file in your current directory called <file_name>.dump if you give name dataloggeraws_backup.dump then it will create file with same name on successfully run command.
- it will ask for passowrd use database_url password to start restore point

Example use to generate file

```
pg_dump -h <database_url> -p <port> -U postgres -d <databasename> -W -F c -b -v -f dataloggeraws_backup.dump
```

2. Create a Database on Local PostgreSQL Server

```
createdb -p <local_port> -U postgres <local_database_name_to_create>
```

- it will ask for passowrd use local database password to create database

3. Restore the Database Backup dump in local

```
pg_restore -h localhost -p <local_port> -U postgres -d <local_database_name_to_create> -v <file_name>.dump
```

- it will ask for passowrd use local database password to start database dump file to database
  Example use to generate file

```
pg_restore -h localhost -p <local_port> -U postgres -d datalogger_local -v dataloggeraws_backup.dump
```

# Deployment

## Requirements

- Postgres instance with transaction isolation set to **serializable**.
- Redis Stack instance.

## Steps

1. Update `package.json` version.

2. Build docker image for destination platform:

```bash
docker build --platform linux/amd64 . -t be-admin-api:<VERSION>
```

3. Save docker image as tar:

```bash
docker save be-admin-api:<VERSION> -o be-admin-api_<VERSION>.tar
```

4. Copy to server:

```bash
scp be-admin-api_<VERSION> <USER>@<SERVER_IP>:<DEST_DIR>
```

5. Log into server and load docker image:

```bash
docker load -i be-admin-api_<VERSION>.tar
```

# MikroORM caveats

## Lazy properties with Ref type - WARNING

When dealing with relationships in our entities, MIkroORM encourages the use of
[Ref types](https://mikro-orm.io/docs/type-safe-relations)
which hold the id of the given entity plus methods to lazy load it if needed.

This can be useful when the whole entity is not needed but we still want a
strong typing for the relationship between entities. The problem is creating a
Ref type without a MikroORM repository (eg: on a constructor) requires calling a
MikroORM static method which doesn't play nice with unit tests (see
[Reference.createFromPK](https://mikro-orm.io/docs/type-safe-relations#assigning-to-reference-properties)).
This is basically because we're keeping a db query hidden in our domain entity.

To avoid this dependency, we still need the ref type to generate the foreign key
constraint migration. However, we will mark it as **private readonly** and add a
separate id property to load the field accordingly.

As an example, this is how it's being addeded for the **updatedBy** field:

```
@ManyToOne({ hidden: true, nullable: true, fieldName: 'updated_by' })
private readonly updatedBy?: Ref<User>;

@Property({ fieldName: 'updated_by' })
updatedById?: UserId;
```

## Create existing DB entity

This steps are required to add an existing DB entity to the project in order to
track it accordingly and allow a fresh db installation.

1. On test DB (with production data loaded), generate entity models:

```bash
MIKRO_ORM_ENV=.test.env npx mikro-orm generate-entities --dump
```

2. Copy the desired entity to a file named `<entity-name>.entity.ts` on its corresponding `domain` folder.

3. On dev DB, generate migration file (**Check migration has all required changes**):

```bash
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create
```

4. Rename migration file to `<timestamp>-<migration-action>-NEWDB.ts`:

- `timestamp`: timestamp set by migration creation.
- `migration-action`: a short spinal case text describing what the migration does.
- `NEWDB`: tag used to identify migration only intended to be run on a clean db.

5. Modify migration to extend `ConditionalMigration`.

   - Import `ConditionalMigration` and make the migration inherit it (must use relative import):

   ```
   import { NewDbMigration } from '../db/ConditionalMigration';
   ```

   - Replace `up` and `down` methods with `conditionalUp` and `conditionalDown`.

   **If the added entity has foreign keys to other entities, MikroORM will use
   another naming strategy**. This will result in duplicate foreign keys when
   working on the existing db. To mitigate this:

   1. Replace the foreign key name with the one used in the existing db.
   2. Add a new migration following the [Modifying DB entities](#modifying-db-entities) steps which drops the old foreign key and creates a new one using MikroORM naming strategy.

6. Run migration with dev DB and `NEW_DB` environment variable flag enabled:

```bash
NEW_DB=true MIKRO_ORM_ENV=.development.env npx mikro-orm migration:up
```

## Modifying DB entities

If an already tracked entity needs any modification follow these steps.

1. Modify entity model.

2. On dev DB, generate migration file (**Check migration has all required changes**):

```bash
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create
```

3. Rename migration file to `<timestamp>-<migration-action>.ts`:

- `timestamp`: timestamp set by migration creation.
- `migration-action`: a short spinal case text describing what the migration does.

MIKRO_ORM_ENV=.development.env yarn migrations:up:dev

## Latest migration script for annotation table

```
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name createAnnotationTable
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name updateDashboardDataField

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name createExcursion
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name createCustomerImage

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name alertMetadata
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name asset_templateAndAsetsReferences
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name change_assetToAssetTemplateReference

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name add_units_group_to_asset

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name nullable_alert_fields

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create  --name add_dashboard_tempate_asset_and_customer_dashboard_template
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name nullable_alert_field_period
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name user_preferene_key


MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name alert_softdelete_keys

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --blank

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_update
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_add_asset_template
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_add_asset_template_nullable
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_add_asset_template_uniques
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_add_asset_template_null
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name calc_metric_asset_template_add_writeback
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name dashboard_size_issue

MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name rename_assettemplate_on_asset
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name rename_assettemplate_snakeCase
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:create --name dashboard_template_unique

```

```
MIKRO_ORM_ENV=.development.env npx mikro-orm migration:up
```
need to added
"@nestjs/bull": "^11.0.2",
"@nestjs/bullmq": "^11.0.2",
"bullmq": "^5.41.7",

PGPASSWORD='DB_PASSWORD' pg_dump \
 --dbname=postgresql://<EMAIL>:5432/dataloggeraws \
 --file=dataloggeraws_backup.sql \
 --format=c \
 --no-password \
 --verbose

PGPASSWORD='DB_PASSWORD' psql \
 --host=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com \
 --port=5432 \
 --username=postgres \
 --no-password \
 --dbname=postgres \
 --command="SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'dataloggertest';"

PGPASSWORD='DB_PASSWORD' psql \
 --host=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com \
 --port=5432 \
 --username=postgres \
 --no-password \
 --command="DROP DATABASE IF EXISTS dataloggertest;"

PGPASSWORD='DB_PASSWORD' psql \
 --host=dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com \
 --port=5432 \
 --username=postgres \
 --no-password \
 --command="CREATE DATABASE dataloggertest;"

PGPASSWORD='DB_PASSWORD' pg_restore \
 --dbname=postgresql://<EMAIL>:5432/dataloggertest \
 --no-password \
 --verbose \
 dataloggeraws_backup.sql
