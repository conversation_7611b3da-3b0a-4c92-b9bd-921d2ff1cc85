import { EntityManager, Loaded } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { Injectable } from "@nestjs/common";
import { AssetType } from "src/assets/domain/asset-type.entity";
import { ExcursionStats } from "../domain/excursion-stats.entity";

@Injectable()
export class AlertStatsRepository {
  constructor(
    @InjectRepository(ExcursionStats)
    private readonly alertStats: EntityRepository<ExcursionStats>,
    @InjectRepository(AssetType)
    private readonly assetTypes: EntityRepository<AssetType>,
    private readonly em: EntityManager
  ) {}

  async getStatByAlert({ id }: { id: number }) {
    const alertStats = await this.alertStats.find({
      alert: {
        id: id,
      },
    });
    return alertStats.map((alertStat) => {
      const { formattedTimeDuration, ...rest } = alertStat;
      return {
        ...rest,
        formattedTimeDuration,
      };
    });
  }

  async getGroupedStats(
    customerId: number,
    start: number,
    end: number,
    interval?: "daily" | "weekly" | "monthly",
    assetId?: number,
    measureId?: number,
    date?: string
  ) {
    if (!start || !end || isNaN(start) || isNaN(end)) {
      throw new Error("Start and End must be valid timestamps.");
    }

    if (!customerId) {
      throw new Error("Customer ID must be provided.");
    }

    const startDate = new Date(start);
    const endDate = new Date(end);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error("Invalid start or end time value.");
    }

    const filters: string[] = [];
    const params: any[] = [];

    filters.push(`AND excursion_stats.start_time BETWEEN ? AND ?`);
    params.push(startDate.toISOString(), endDate.toISOString());

    if (measureId) {
      filters.push(`AND alerts.measurement_id = ?`);
      params.push(measureId);
    }
    if (assetId) {
      filters.push(`AND alerts.asset_id = ?`);
      params.push(assetId);
    }
    if (customerId) {
      filters.push(`AND alerts.customer_id = ?`);
      params.push(customerId);
    }

    let intervalTrunc = "day"; // default fallback
    if (date) {
      if (!interval || !["daily", "weekly", "monthly"].includes(interval)) {
        throw new Error(
          "Interval is required and must be valid when using 'date' filter."
        );
      }
      intervalTrunc =
        interval === "daily" ? "day" : interval === "weekly" ? "week" : "month";
      filters.push(
        `AND DATE_TRUNC(?, excursion_stats.start_time) = DATE_TRUNC(?, TIMESTAMP ?)`
      );
      params.push(intervalTrunc, intervalTrunc, date);
    }

    const query = `
    SELECT
      excursion_stats.alert_id,
      excursion_stats.start_time AS period_start,
      excursion_stats.end_time AS period_end,
      COUNT(*) AS excursion_count,
      SUM(EXTRACT(EPOCH FROM excursion_stats.time_duration)) AS total_duration_seconds,
      AVG(excursion_stats.avg_value) AS avg_value,
      MIN(excursion_stats.min_value) AS min_value,
      MAX(excursion_stats.max_value) AS max_value,
      alerts.threshold_type,
      alerts.agg,
      alerts.period,
      alerts.state,
      alerts.enabled,
      alerts.description,
      asset.a_type,
      measurement.m_type,
      measurement.metric,
      measurement.tag
    FROM excursion_stats
    JOIN alerts ON excursion_stats.alert_id = alerts.id
    JOIN asset ON alerts.asset_id = asset.id
    JOIN measurement ON alerts.measurement_id = measurement.id
    WHERE 1=1
    ${filters.join(" ")}
    GROUP BY
      excursion_stats.alert_id, excursion_stats.start_time, excursion_stats.end_time,
      alerts.threshold_type, alerts.agg, alerts.period, alerts.state, alerts.enabled,
      alerts.description,
      asset.a_type, measurement.m_type, measurement.metric, measurement.tag
    ORDER BY
      excursion_stats.alert_id, period_start, period_end;
  `;

    return this.em.getConnection().execute(query, params, "all");
  }

  async getRangeGroupedStats(customer: number, start?: number, end?: number) {
    const startTime = start ? new Date(Number(start)) : undefined;
    const endTime = end ? new Date(Number(end)) : undefined;
    if (start === undefined || end === undefined) {
      throw new Error(
        `Invalid date range. Start and end timestamps must be provided.`
      );
    }
    if (
      (start !== undefined && Number.isNaN(start)) ||
      (end !== undefined && Number.isNaN(end))
    ) {
      throw new Error(
        `Invalid date range. Start and end must be valid timestamps.`
      );
    }

    const allExcursions = await this.alertStats.find(
      {
        start_time: { $gte: startTime, $lte: endTime },
        end_time: { $gte: startTime, $lte: endTime },
        alert: {
          customerId: customer,
        },
      },
      {
        populate: [
          "alert.thresholdType",
          "alert.asset.assetType",
          "alert.measurement.measurementType",
        ],
      }
    );

    const thresholds: Record<string, number> = {};
    const assetTypes: Record<string, number> = {};
    const measurementTypes: Record<string, number> = {};
    let totalDurationSeconds = 0;
    const assetTypesIds = new Set<number>();
    for (const excursion of allExcursions) {
      // Collect unique asset type IDs
      const assetTypeId = excursion.alert?.asset?.assetType?.get().id;
      if (assetTypeId) {
        assetTypesIds.add(assetTypeId);
      }
    }
    const allAssetTypes = await this.assetTypes.find(
      {
        id: { $in: Array.from(assetTypesIds) },
      },
      {
        fields: ["id", "name", "parentType"],
      }
    );

    const assetTypeById: Record<
      number,
      Loaded<AssetType, "id" | "name" | "lowerCaseName" | "parentType">
    > = {};
    for (const assetType of allAssetTypes) {
      assetTypeById[assetType.id] = assetType;
    }

    const assetTypeToParentsMap: Record<number, string> = {};

    for (const assetType of allAssetTypes) {
      const parents: string[] = [];
      let current = assetType;
      parents.push(current.name);
      while (current.parentType) {
        const parent = current.parentType.get();
        if (!parent) break;
        parents.push(parent.name);
        current = assetTypeById[parent.id];
        if (!current) break;
      }

      assetTypeToParentsMap[assetType.id] = parents.join(" > ");
    }
    for (const excursion of allExcursions) {
      // Count by thresholdType
      const thresholdType =
        excursion.alert?.thresholdType?.threshold || "unknown";
      thresholds[thresholdType] = (thresholds[thresholdType] || 0) + 1;

      // Count by assetType
      // Count by assetType using full hierarchy
      const assetTypeEntity = excursion.alert?.asset?.assetType?.get();
      const assetTypePath =
        (assetTypeEntity && assetTypeToParentsMap[assetTypeEntity.id]) ||
        "unknown";
      assetTypes[assetTypePath] = (assetTypes[assetTypePath] || 0) + 1;

      // Count by measurementType
      const measurementType =
        excursion.alert?.measurement?.measurementType?.name || "unknown";
      measurementTypes[measurementType] =
        (measurementTypes[measurementType] || 0) + 1;

      // Sum durations
      if (excursion.time_duration) {
        const {
          days = 0,
          hours = 0,
          minutes = 0,
          seconds = 0,
          milliseconds = 0,
        } = excursion.time_duration;
        totalDurationSeconds +=
          days * 86400 +
          hours * 3600 +
          minutes * 60 +
          seconds +
          milliseconds / 1000;
      }
    }

    const hours = Math.floor(totalDurationSeconds / 3600);
    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
    const seconds = Math.floor(totalDurationSeconds % 60);
    const totalDurationHHMMSS = [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0"),
    ].join(":");

    return {
      totalDeviation: totalDurationHHMMSS,
      total: allExcursions.length,
      thresholds,
      assetTypes,
      measurementTypes,
    };
  }

  async getAllExcursionStats() {
    return await this.alertStats.findAll({
      populate: ["alert.measurement"], // Populate the alert and measurement relationships
    });
  }
}
