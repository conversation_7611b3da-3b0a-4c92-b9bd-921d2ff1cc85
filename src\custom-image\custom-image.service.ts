import { Injectable, NotFoundException } from "@nestjs/common";
import { CustomerService } from "src/customers/customer.service";
import { User } from "src/users/domain/user.entity";
import { CustomImageRepository } from "./repository/custom-image.repository";
import { CustomImage } from "./domain/custom-image.entity";
import { Reference } from "@mikro-orm/core";

@Injectable()
export class CustomImageService {
  constructor(
    private readonly customImageRepository: CustomImageRepository,
    private readonly customerService: CustomerService
  ) {}

  async create({
    customerId,
    logo,
    user,
  }: {
    customerId: number | null;
    logo: string;
    user: User;
  }) {
    let findCustomer = null;
    if (customerId !== null) {
      findCustomer = await this.customerService.findById(customerId);
      if (!findCustomer) {
        throw new NotFoundException("Customer not found");
      }
    }
    const customImage = new CustomImage();
    customImage.createdAt = new Date();
    customImage.createdBy = Reference.createFromPK(User, user.id);
    customImage.logo = logo;
    customImage.customer = findCustomer;
    return await this.customImageRepository.createCustomImage(customImage);
  }

  async getCustomerImage(customerId: number) {
    return await this.customImageRepository.getCustomImage(customerId);
  }

  async getImage(imageId: number) {
    return await this.customImageRepository.getImage(imageId);
  }

  async getGlobalImages() {
    return await this.customImageRepository.getGlobalImages();
  }
}
