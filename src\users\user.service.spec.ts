import { MikroORM } from '@mikro-orm/core';
import { Logger, NotFoundException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { UserCreationParams, User } from './domain/user.entity';
import { PasswordEncoder } from '../security/password-encoder.service';
import { UserService } from './user.service';
import { UserRepository } from './repository/user.repository';
import { Role } from 'src/authorization/domain/customer-user-role.entity';
import { CustomerService } from 'src/customers/customer.service';
import { Customer } from 'src/customers/domain/customer.entity';
import { createMikroOrmTestModule } from 'src/db/__testing__/factories';
import { testUserFactory } from './__tests__/factories';

describe('UserService', () => {
  const uniqueUser: UserCreationParams = {
    username: 'test',
    password: 'strongpassword',
    email: '<EMAIL>',
    globalRole: Role.ADMIN,
    firstName: 'f',
    lastName: 'l',
  };

  describe('create', () => {
    test('user with unique username and email should be created with a hashed password', async () => {
      const { userService, repositoryMock } = await createUserService();

      await userService.create(uniqueUser, null);

      const createdUser = repositoryMock.add.mock.calls[0][0] as User;
      expect(createdUser.username).toBe('test');
      expect(createdUser.enabled).toBeTruthy();
      expect(createdUser.email).toBe('<EMAIL>');
      expect(createdUser.globalRole).toBe(Role.ADMIN);
      expect(createdUser.password).toBe('strongpassword_hashed');
      expect(createdUser.firstName).toBe('f');
      expect(createdUser.lastName).toBe('l');
    });

    test('user with invalid username sould throw an exception', async () => {
      const { userService } = await createUserService();

      await expect(() =>
        userService.create(
          { ...uniqueUser, username: 'inValId username' },
          null,
        ),
      ).rejects.toThrow('Invalid username');
    });

    test('scoped user with non existing customer should throw an exception', async () => {
      const { userService } = await createUserService();

      await expect(() =>
        userService.create(
          {
            ...uniqueUser,
            globalRole: undefined,
            scopedRoles: [{ role: Role.ADMIN, customerIds: [404] }],
          },
          null,
        ),
      ).rejects.toThrow('Customer id does not exist');
    });
  });

  describe('update', () => {

    test('first name to existing user should change it', async () => {
      const { userService, repositoryMock, globalAdminId } = await createUserService();

      await userService.update(globalAdminId, { firstName: 'John' }, 7, 3);

      expect(repositoryMock.update.mock.calls.length).toBe(1);
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.firstName).toBe('John');
    });

    test('first name to non existing user should throw NotFoundException', async () => {
      const { userService } = await createUserService();

      await expect(
        userService.update(404, { firstName: 'John' }, 7, 4),
      ).rejects.toThrowError(new NotFoundException('User does not exist'));
    });

    test('with existing email should throw an exception', async () => {
      const { userService, globalAdminId, existingEmail } =
        await createUserService();

      await expect(
        userService.update(globalAdminId, { email: existingEmail }, 7, 4),
      ).rejects.toThrowError('Email is already in use');
    });

    test('with non existing email should update', async () => {
      const { userService, repositoryMock, globalAdminId } =
        await createUserService();

      await userService.update(
        globalAdminId,
        { email: '<EMAIL>' },
        7,
        4,
      );

      expect(repositoryMock.update.mock.calls.length).toBe(1);
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.email).toBe('<EMAIL>');
    });

    test('password should store result of password hasher', async () => {
      const {
        userService,
        repositoryMock,
        globalAdminId,
        mockPasswordEncoder,
      } = await createUserService();

      await userService.update(
        globalAdminId,
        { password: 'newpassword' },
        7,
        4,
      );

      expect(mockPasswordEncoder.hash.mock.calls[0][0]).toBe('newpassword');
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.password).toBe('newpassword_hashed');
    });

    test('disabling user should update', async () => {
      const { userService, repositoryMock, globalAdminId } =
        await createUserService();

      await userService.update(globalAdminId, { enabled: false }, 7, 4);

      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.enabled).toBeFalsy();
    });

    test('global role should update accordingly', async () => {
      const { userService, globalAdminId, repositoryMock } =
        await createUserService();

      await userService.update(
        globalAdminId,
        { globalRole: Role.POWER_USER },
        7,
        3,
      );

      expect(repositoryMock.update.mock.calls.length).toBe(1);
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.globalRole).toBe(Role.POWER_USER);
    });

    test('empty scoped roles should update accordingly', async () => {
      const { userService, globalAdminId, repositoryMock } =
        await createUserService();

      await userService.update(globalAdminId, { scopedRoles: [] }, 7, 3);

      expect(repositoryMock.update.mock.calls.length).toBe(1);
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.globalRole).toBeUndefined();
      expect(updatedUser.scopedRoles).toStrictEqual([]);
    });

    test('new scoped roles should update accordingly', async () => {
      const { userService, globalAdminId, repositoryMock } =
        await createUserService();

      await userService.update(
        globalAdminId,
        { scopedRoles: [{ role: Role.POWER_USER, customerIds: [4] }] },
        7,
        3,
      );

      expect(repositoryMock.update.mock.calls.length).toBe(1);
      const updatedUser = repositoryMock.update.mock.calls[0][0] as User;
      expect(updatedUser.globalRole).toBeUndefined();
      expect(updatedUser.scopedRoles).toStrictEqual([
        { role: Role.POWER_USER, customerIds: [4] },
      ]);
    });

    test('scoped roles with non existing customer should throw an exeption', async () => {
      const { userService, globalAdminId } = await createUserService();

      await expect(
        userService.update(
          globalAdminId,
          { scopedRoles: [{ role: Role.POWER_USER, customerIds: [404] }] },
          7,
          3,
        ),
      ).rejects.toThrow('Customer id does not exist');
    });
  });

  test('getAllByCustomerId should call user repository query with customer id', async () => {
    const { userService, repositoryMock } = await createUserService();

    // await userService.getAllByCustomerId(43);

    expect(repositoryMock.getAllByCustomerId.mock.calls.length).toBe(1);
    expect(repositoryMock.getAllByCustomerId.mock.calls[0][0]).toBe(43);
  });
});

const createUserService = async () => {
  const mockPasswordEncoder: jest.Mocked<Pick<PasswordEncoder, 'hash'>> = {
    hash: jest.fn(async (password) => `${password}_hashed`),
  };
  const globalAdminId = 42;
  const scopedAdminId = 21;
  const existingEmail = '<EMAIL>';
  const repositoryMock = {
    add: jest.fn(),
    getAllByCustomerId: jest.fn(),
    update: jest.fn(),
    findById: jest.fn(async (id) => {
      if (id === globalAdminId) {
        return testUserFactory.createGlobalScopeUser('test', Role.ADMIN);
      } else if (id === scopedAdminId) {
        return testUserFactory.createCustomerScopedUser('scoped_admin', [
          {
            role: Role.ADMIN,
            customerIds: [3],
          },
        ]);
      } else {
        return null;
      }
    }),
    findByEmail: jest.fn(async (email) =>
      email === existingEmail
        ? testUserFactory.createGlobalScopeUser('existingemial', Role.USER)
        : null,
    ),
  } as {
    add: jest.Mock,
    getAllByCustomerId: jest.Mock,
    update: jest.Mock,
    findById: jest.Mock,
    findByEmail: jest.Mock,
  };

  const customerServiceMock: jest.Mocked<
    Pick<CustomerService, 'allExistById'>
  > = {
    allExistById: jest.fn(async (ids) => (ids.includes(404) ? false : true)),
  };

  const moduleRef = await Test.createTestingModule({
    imports: [createMikroOrmTestModule([User, Customer])],
    providers: [
      {
        provide: CustomerService,
        useValue: customerServiceMock,
      },
      {
        provide: UserRepository,
        useValue: repositoryMock,
      },
      {
        provide: MikroORM,
        useValue: jest.fn(),
      },
      {
        provide: Logger,
        useValue: jest.fn(),
      },
      {
        provide: PasswordEncoder,
        useValue: mockPasswordEncoder,
      },
      UserService,
    ],
  }).compile();

  const userService = moduleRef.get(UserService);

  return {
    repositoryMock,
    userService,
    globalAdminId,
    existingEmail,
    mockPasswordEncoder,
  };
};
