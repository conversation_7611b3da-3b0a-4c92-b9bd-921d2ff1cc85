import { EntityRepository } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable } from "@nestjs/common";
import { CustomImage } from "../domain/custom-image.entity";
@Injectable()
export class CustomImageRepository {
  constructor(
    @InjectRepository(CustomImage)
    private readonly customImageEntity: EntityRepository<CustomImage>
  ) {}

  async createCustomImage(customImageReference: CustomImage) {
    await this.customImageEntity.persistAndFlush(customImageReference);
    return customImageReference;
  }
  async getCustomImage(customerId: number) {
    return await this.customImageEntity.find({
      customer: {
        id: customerId,
      },
    });
  }
  async getImage(imageId: number) {
    return await this.customImageEntity.findOne({
      id: imageId,
    });
  }

  async getGlobalImages() {
    return await this.customImageEntity.find({
      customer: null,
    });
  }
}
