import { EntityManager } from "@mikro-orm/core";
import { EntityRepository } from "@mikro-orm/postgresql";
import { Test, TestingModule } from "@nestjs/testing";
import { AssetService } from "src/assets/asset.service";
import { AssetType } from "src/assets/domain/asset-type.entity";
import { MetricService } from "src/assets/metric.service";
import { AssetRepository } from "src/assets/repository/asset.repository";
import { CalculationInput } from "src/calc-engine/domain/calculation-input.entity";
import { CalculationInstance } from "src/calc-engine/domain/calculation-instance.entity";
import { CalcEngineRepository } from "src/calc-engine/repository/calc-engine.repository";
import { CalculationMetricInput } from "src/calc-metrics-controller/domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { AssetMeasurementService } from "src/measurements/asset-measurement.service";
import { Datasource } from "src/measurements/domain/datasource.entity";
import { UnitsGroupService } from "src/measurements/units-group.service";
import { User } from "src/users/domain/user.entity";
import { AssetTemplateMeasurement } from "../domain/asset-template-measurement.entity";
import { AssetTemplate } from "../domain/asset-template.entity";
import { AssetTemplateRepository } from "./asset-template.repository";

describe("AssetTemplateRepository", () => {
  let repository: AssetTemplateRepository;
  let mockEntityRepository: jest.Mocked<EntityRepository<AssetTemplate>>;
  let mockAssetTemplateMeasurementRepo: jest.Mocked<
    EntityRepository<AssetTemplateMeasurement>
  >;
  let mockDataSourceRepo: jest.Mocked<EntityRepository<Datasource>>;
  let mockCalculationMetricInstanceRepo: jest.Mocked<
    EntityRepository<CalculationMetricInstance>
  >;
  let mockCalculationMetricInputRepo: jest.Mocked<
    EntityRepository<CalculationMetricInput>
  >;
  let mockCalcInstanceRepository: jest.Mocked<
    EntityRepository<CalculationInstance>
  >;
  let mockCalcInputRepository: jest.Mocked<EntityRepository<CalculationInput>>;
  let mockMetricService: jest.Mocked<MetricService>;
  let mockCalcEngineRepo: jest.Mocked<CalcEngineRepository>;
  let mockAssetRepository: jest.Mocked<AssetRepository>;
  let mockAssetService: jest.Mocked<AssetService>;
  let mockAssetMeasurementService: jest.Mocked<AssetMeasurementService>;
  let mockUnitsGroupService: jest.Mocked<UnitsGroupService>;
  let mockTransactionFactory: jest.Mocked<TransactionFactory>;
  let mockEntityManager: jest.Mocked<EntityManager>;

  beforeEach(async () => {
    // Create mocks
    mockEntityRepository = {
      persistAndFlush: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      create: jest.fn(),
    } as any;

    mockAssetTemplateMeasurementRepo = {
      find: jest.fn(),
      nativeDelete: jest.fn(),
      create: jest.fn(),
      persistAndFlush: jest.fn(),
    } as any;

    mockDataSourceRepo = {
      findAll: jest.fn(),
    } as any;

    mockCalculationMetricInstanceRepo = {
      find: jest.fn(),
      nativeDelete: jest.fn(),
    } as any;

    mockCalculationMetricInputRepo = {
      find: jest.fn(),
      nativeDelete: jest.fn(),
    } as any;

    mockCalcInstanceRepository = {} as any;
    mockCalcInputRepository = {} as any;

    mockMetricService = {
      findById: jest.fn(),
    } as any;

    mockCalcEngineRepo = {
      createCalcInputs: jest.fn(),
      updateCalcInputs: jest.fn(),
    } as any;

    mockAssetRepository = {
      getAllByAssetTemplateId: jest.fn(),
    } as any;

    mockAssetService = {} as any;

    mockAssetMeasurementService = {
      getAll: jest.fn(),
      removeById: jest.fn(),
      update: jest.fn(),
      create: jest.fn(),
    } as any;

    mockUnitsGroupService = {
      findMeasurementTypeDefaultUnit: jest.fn(),
    } as any;

    mockEntityManager = {
      create: jest.fn(),
      persistAndFlush: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      remove: jest.fn(),
    } as any;

    mockTransactionFactory = {
      run: jest
        .fn()
        .mockImplementation((callback) => callback(mockEntityManager)),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AssetTemplateRepository,
        { provide: "AssetTemplateRepository", useValue: mockEntityRepository },
        {
          provide: "AssetTemplateMeasurementRepository",
          useValue: mockAssetTemplateMeasurementRepo,
        },
        { provide: "DatasourceRepository", useValue: mockDataSourceRepo },
        {
          provide: "CalculationMetricInstanceRepository",
          useValue: mockCalculationMetricInstanceRepo,
        },
        {
          provide: "CalculationMetricInputRepository",
          useValue: mockCalculationMetricInputRepo,
        },
        {
          provide: "CalculationInstanceRepository",
          useValue: mockCalcInstanceRepository,
        },
        {
          provide: "CalculationInputRepository",
          useValue: mockCalcInputRepository,
        },
        { provide: MetricService, useValue: mockMetricService },
        { provide: CalcEngineRepository, useValue: mockCalcEngineRepo },
        { provide: AssetRepository, useValue: mockAssetRepository },
        { provide: AssetService, useValue: mockAssetService },
        {
          provide: AssetMeasurementService,
          useValue: mockAssetMeasurementService,
        },
        { provide: UnitsGroupService, useValue: mockUnitsGroupService },
        { provide: TransactionFactory, useValue: mockTransactionFactory },
      ],
    }).compile();

    repository = module.get<AssetTemplateRepository>(AssetTemplateRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  describe("findById", () => {
    it("should find asset template by id without filter", async () => {
      // Arrange
      const id = 1;
      const mockAssetTemplate = { id: 1, manufacturer: "Test" } as any;
      mockEntityRepository.findOne.mockResolvedValue(mockAssetTemplate);

      // Act
      const result = await repository.findById(id);

      // Assert
      expect(mockEntityRepository.findOne).toHaveBeenCalledWith({ id });
      expect(result).toBe(mockAssetTemplate);
    });

    it("should find asset template by id with assetTypeId filter", async () => {
      // Arrange
      const id = 1;
      const assetTypeId = 2;
      const mockAssetTemplate = { id: 1, manufacturer: "Test" } as any;
      mockEntityRepository.findOne.mockResolvedValue(mockAssetTemplate);

      // Act
      const result = await repository.findById(id, { assetTypeId });

      // Assert
      expect(mockEntityRepository.findOne).toHaveBeenCalledWith({
        id,
        assetType: { id: assetTypeId },
      });
      expect(result).toBe(mockAssetTemplate);
    });

    it("should return null when asset template not found", async () => {
      // Arrange
      const id = 999;
      mockEntityRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await repository.findById(id);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe("getAllByAssetTypeId", () => {
    it("should return asset templates for given asset type and customer", async () => {
      // Arrange
      const assetTypeId = 1;
      const customerId = 2;
      const mockTemplates = [
        { id: 1, manufacturer: "Test1" },
        { id: 2, manufacturer: "Test2" },
      ] as any;

      mockEntityRepository.find.mockResolvedValue(mockTemplates);

      // Act
      const result = await repository.getAllByAssetTypeId(
        assetTypeId,
        customerId
      );

      // Assert
      expect(mockEntityRepository.find).toHaveBeenCalledWith({
        assetType: { id: assetTypeId },
        $or: [{ customer: null }, { customer: { id: customerId } }],
      });
      expect(result).toBe(mockTemplates);
    });
  });

  describe("getAssetTemplateWithAssetTypeAndId", () => {
    it("should find asset template with specific asset type and template id", async () => {
      // Arrange
      const assetTypeId = 1;
      const templateId = 2;
      const mockTemplate = { id: 2, assetType: { id: 1 } } as any;

      mockEntityRepository.findOne.mockResolvedValue(mockTemplate);

      // Act
      const result = await repository.getAssetTemplateWithAssetTypeAndId(
        assetTypeId,
        templateId
      );

      // Assert
      expect(mockEntityRepository.findOne).toHaveBeenCalledWith({
        $and: [{ assetType: { id: assetTypeId } }, { id: templateId }],
      });
      expect(result).toBe(mockTemplate);
    });
  });

  describe("getAssociatedAssets", () => {
    it("should return associated assets for given template id", async () => {
      // Arrange
      const assetTemplateId = 1;
      const mockAssets = [{ id: 1 }, { id: 2 }] as any;

      mockAssetRepository.getAllByAssetTemplateId.mockResolvedValue(mockAssets);

      // Act
      const result = await repository.getAssociatedAssets(assetTemplateId);

      // Assert
      expect(mockAssetRepository.getAllByAssetTemplateId).toHaveBeenCalledWith(
        assetTemplateId
      );
      expect(result).toBe(mockAssets);
    });
  });

  describe("updateAssetTemplateMulti", () => {
    let mockUser: User;
    let mockAssetType: AssetType;
    let mockAssetTemplate: AssetTemplate;

    beforeEach(() => {
      mockUser = { id: 1, username: "testuser" } as User;
      mockAssetType = { id: 1, name: "TestType" } as AssetType;
      mockAssetTemplate = {
        id: 1,
        manufacturer: "Old Manufacturer",
        modelNumber: "Old Model",
        measurements: {
          getItems: jest.fn().mockReturnValue([]),
          remove: jest.fn(),
          add: jest.fn(),
        },
      } as any;
    });

    it("should throw error when asset template not found", async () => {
      // Arrange
      const templateId = 999;
      const updateData = {
        manufacturer: "New Manufacturer",
        modelNumber: "New Model",
        assetTypeId: mockAssetType,
        measurement: [],
      };

      mockEntityRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        repository.updateAssetTemplateMulti({
          assetTypeId: mockAssetType,
          templateId,
          createdBy: mockUser,
          updateData,
          headers: {} as any,
        })
      ).rejects.toThrow("Asset template not found!");
    });

    it("should handle metric not found in expression variables", async () => {
      // Arrange
      const templateId = 1;
      const updateData = {
        manufacturer: "New Manufacturer",
        modelNumber: "New Model",
        assetTypeId: mockAssetType,
        measurement: [],
      };

      const expressionInstance = [
        {
          metricId: 1,
          iswriteback: false,
          pollPeriod: 60,
          templateId: 1,
          ispersisted: true,
          variables: [
            {
              inputLabel: "input1",
              comment: "test comment",
              metric: 999, // Non-existent metric
              constantType: "number" as const,
              constantValue: null,
            },
          ],
        },
      ] as any;

      mockEntityRepository.findOne.mockResolvedValue(mockAssetTemplate as any);
      mockDataSourceRepo.findAll.mockResolvedValue([]);
      mockAssetTemplateMeasurementRepo.find.mockResolvedValue([]);
      mockAssetRepository.getAllByAssetTemplateId.mockResolvedValue([]);
      mockMetricService.findById.mockResolvedValue(null);
      mockEntityManager.create.mockReturnValue({} as any);

      // Act & Assert
      await expect(
        repository.updateAssetTemplateMulti({
          assetTypeId: mockAssetType,
          templateId,
          createdBy: mockUser,
          updateData,
          headers: {} as any,
          expressionInstance,
        })
      ).rejects.toThrow(TypeError);
    });
  });
});
