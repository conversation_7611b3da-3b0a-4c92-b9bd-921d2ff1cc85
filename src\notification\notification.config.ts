export const notificationConfig = () => ({
  notification: {
    send: process.env.SEND_NOTIFICATIONS === "true" || false,
    sendSMS: process.env.SEND_SMS_NOTIFICATIONS === "true" || false,
    account_sid: process.env.TWILLIO_ACCOUNT_SID || "",
    auth_token: process.env.TWILLIO_AUTH_TOKEN || "",
    email_api_token: process.env.TWILLIO_EMAIL_API_TOKEN || "",
    email_from: process.env.EMAIL_FROM || "",
    sms_from_no: process.env.SMS_FROM_NO || "",
    access_key_id: process.env.ACCESS_KEY_ID || "",
    secret_access_key: process.env.SECRET_ACCESS_KEY || "",
    region: process.env.REGION || "us-east-1",
  },
});

export type NotificationConfig = ReturnType<typeof notificationConfig>;
