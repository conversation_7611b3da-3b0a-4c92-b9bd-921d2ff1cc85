import { UniqueConstraintViolationException } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { ForbiddenException, NotFoundException } from "@nestjs/common";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { Customer } from "src/customers/domain/customer.entity";
import { DashboardTemplate } from "src/dashboard-template/domain/dashboard-template.entity";
import { Widget } from "src/dashboards/domain/dashboard.types";
import { User } from "src/users/domain/user.entity";

export class DashboardTemplateRepository {
  constructor(
    @InjectRepository(DashboardTemplate)
    private readonly dashboardTemplates: EntityRepository<DashboardTemplate>,
    @InjectRepository(Customer)
    private readonly customers: EntityRepository<Customer>,
    @InjectRepository(AssetTemplate)
    private readonly assetTemplates: EntityRepository<AssetTemplate>
  ) {}

  async createDashboardTemplate({
    asset_template,
    data,
    title,
    authUser,
    customerId,
  }: {
    asset_template: number;
    title: string;
    data: string;
    authUser: User;
    customerId?: number;
  }) {
    const existingAssetTemplate = await this.assetTemplates.findOne(
      asset_template
    );
    if (!existingAssetTemplate) {
      throw new NotFoundException("Asset template not found");
    }
    const dashboardTemplate = new DashboardTemplate();
    dashboardTemplate.asset_template = existingAssetTemplate;
    dashboardTemplate.createdby = authUser;
    dashboardTemplate.createdat = new Date();
    dashboardTemplate.title = title;
    dashboardTemplate.data = data;
    dashboardTemplate.createdat = new Date();
    if (customerId !== undefined) {
      const customer = await this.customers.findOne({
        id: customerId,
      });
      if (!customer) {
        throw new NotFoundException("Customer not found");
      }
      dashboardTemplate.customer = customer;
    }
    try {
      await this.dashboardTemplates.persistAndFlush(dashboardTemplate);
    } catch (error: any) {
      // Handle unique constraint violation
      if (error.code === "23505") {
        // Unique violation error
        if (dashboardTemplate.customer) {
          throw new UniqueConstraintViolationException(
            new Error(
              `Dashboard template with title "${title}" already exists for this customer.`
            )
          );
        }
        // If customer is null, it means it's a global template
        throw new UniqueConstraintViolationException(
          new Error(
            `Dashboard template with title "${title}" already exists globally.`
          )
        );
      } else {
        throw error; // Re-throw other errors
      }
    }
    return {
      ...dashboardTemplate,
      createdby: dashboardTemplate.createdby.id,
    };
  }
  async updateDashboardTemplate({
    id,
    asset_template,
    data,
    title,
    authUser,
    customerId,
  }: {
    id: number;
    customerId?: number;
    asset_template: number;
    title: string;
    data: string;
    authUser: User;
  }) {
    const existingDashboardTemplate = await this.findDashboardTemplateById(
      id,
      customerId
    );
    if (!existingDashboardTemplate) {
      throw new NotFoundException("Dashboard template not found :" + id);
    }

    const existingAssetTemplate = await this.assetTemplates.findOne(
      asset_template
    );
    if (!existingAssetTemplate) {
      throw new NotFoundException("Asset template not found");
    }
    if (customerId !== undefined) {
      const customer = await this.customers.findOne({
        id: customerId,
      });
      if (!customer) {
        throw new NotFoundException("Customer not found");
      }
      existingDashboardTemplate.customer = customer;
    }
    if (customerId === undefined) {
      existingDashboardTemplate.customer = null;
    }
    existingDashboardTemplate.asset_template = existingAssetTemplate;
    existingDashboardTemplate.title = title;
    existingDashboardTemplate.data = data;
    existingDashboardTemplate.updatedby = authUser;

    existingDashboardTemplate.updatedat = new Date();
    try {
      await this.dashboardTemplates.persistAndFlush(existingDashboardTemplate);
    } catch (error: any) {
      // Handle unique constraint violation
      if (error.code === "23505") {
        // Unique violation error
        if (existingDashboardTemplate.customer) {
          throw new UniqueConstraintViolationException(
            new Error(
              `Dashboard template with title "${title}" already exists for this customer.`
            )
          );
        } else {
          // If customer is null, it means it's a global template
          throw new UniqueConstraintViolationException(
            new Error(
              `Dashboard template with title "${title}" already exists globally.`
            )
          );
        }
      } else {
        throw error; // Re-throw other errors
      }
    }
  }

  async findAll(assetTypeId?: string, customerId?: number) {
    return this.getFilteredDashboardTemplates(Number(assetTypeId), customerId);
  }

  // Helper function to fetch dashboard templates
  private async getFilteredDashboardTemplates(
    assetTypeId?: number,
    customerId?: number
  ) {
    const filter = assetTypeId
      ? { asset_template: { assetType: assetTypeId } }
      : undefined;
    const templates = await this.dashboardTemplates.find(
      {
        ...filter,
        $or: [
          {
            customer: null,
          },
          {
            customer: { id: customerId },
          },
        ],
      },
      {
        populate: ["asset_template"],
      }
    );

    return templates.map((template) => {
      const { data, createdby, updatedby, deleted_by, ...rest } = template;
      return {
        createdby: createdby.id,
        updatedby: updatedby?.id ?? null,
        deleted_by: deleted_by?.id ?? null,
        ...rest,
      };
    });
  }

  async findDashboardTemplateById(id: number, customerId: number) {
    const template = await this.dashboardTemplates.findOne(id, {
      populate: ["asset_template"],
    });
    if (!template) {
      throw new NotFoundException("Dashboard template not found");
    }
    if (
      template.customer !== null &&
      String(template.customer.id) !== String(customerId)
    ) {
      throw new ForbiddenException("Access to this dashboard is restricted.");
    }
    return template;
  }
  async deleteDashboardTemplate(id: number) {
    const dashboardTemplate = await this.dashboardTemplates.findOne(id);
    if (!dashboardTemplate) {
      throw new NotFoundException("Dashboard template not found");
    }
    await this.dashboardTemplates.removeAndFlush(dashboardTemplate);
  }

  async createDashboardFromTemplate(id: number, customerId: number) {
    const dashboardTemplate = await this.findDashboardTemplateById(
      id,
      customerId
    );
    if (!dashboardTemplate) {
      throw new NotFoundException("Dashboard template not found");
    }
    // const dashboard = new Dashboard();
    // dashboard.asset = dashboardTemplate.asset_template;
    // dashboard.title = dashboardTemplate.title;
    // dashboard.data = dashboardTemplate.data;
    // await this.dashboards.persistAndFlush(dashboard);
    // return {
    //   ...dashboard,
    //   asset: dashboard.asset.id,
    // };
  }

  async autoSyncDashboardTempate(id: number, customerId: number) {
    const dashboardTemplate = await this.findDashboardTemplateById(
      id,
      customerId
    );
    if (!dashboardTemplate) {
      throw new NotFoundException("Dashboard template not found");
    }
    const dashboardData = JSON.parse(dashboardTemplate.data); // Parse existing data
    const metricIds = dashboardTemplate.asset_template.measurements.map(
      (measure) => measure.metric.id.toString()
    );
    const metricsIdsNames =
      dashboardTemplate.asset_template.measurements.reduce(
        (acc: Record<string, string>, measure) => {
          if (measure.metric?.id && measure.metric?.name) {
            acc[measure.metric.id.toString()] = measure.metric.name;
          }
          return acc;
        },
        {}
      );
    const widgets = dashboardData.widget.widgets as Widget[];
    try {
      const updatedWidgets = await Promise.all(
        widgets.map(async (widget) => {
          const { type, settings } = widget;
          switch (type) {
            case "stats":
            case "kpi-percentage":
            case "kpi-bar-chart":
            case "kpi-sparkline":
            case "kpi-value-indicator":
            case "real-time":
            case "image-stats":
            case "kpi-color-box": {
              const findMetric = metricIds.includes(
                settings.selectedDbMeasureId
              );
              settings.selectedDbMeasureId = findMetric
                ? settings.selectedDbMeasureId
                : "";
              break;
            }
            case "table":
            case "kpi-table": {
              settings.selectedTitles = settings.selectedTitles.filter(
                (title) => metricIds.includes(title.toString())
              );
              break;
            }
            case "alert-widget": {
              settings.selectedTitles = settings.selectedTitles.filter(
                (title) => metricIds.includes(title.toString())
              );
              break;
            }
            case "image": {
              settings.selectedTitles = settings.selectedTitles.filter(
                (title) => metricIds.includes(title)
              );
              settings.dbMeasureIdToName = Object.fromEntries(
                Object.entries(settings.dbMeasureIdToName).filter(([key]) =>
                  metricIds.includes(key)
                )
              );
              settings.labelAndUnits = Object.fromEntries(
                Object.entries(settings.labelAndUnits).filter(([key]) =>
                  metricIds.includes(key)
                )
              );
              settings.measureIdToImageTextDetails = Object.fromEntries(
                Object.entries(settings.measureIdToImageTextDetails).filter(
                  ([key]) => metricIds.includes(key)
                )
              );
              break;
            }
            case "map": {
              settings.markers = settings.markers.map((marker) => {
                marker.selectedTitles = marker.selectedTitles.filter((title) =>
                  metricIds.includes(title)
                );
                marker.labelAndUnits = Object.keys(marker.labelAndUnits).reduce(
                  (
                    acc: Record<
                      string,
                      { label: string; unit: string; value: string }
                    >,
                    key
                  ) => {
                    if (metricIds.includes(key)) {
                      acc[key] = marker.labelAndUnits[key]; // Retain only matching keys
                    }
                    return acc;
                  },
                  {}
                );
                return marker;
              });
              break;
            }
            case "chart": {
              switch (settings.chartType) {
                case "bar":
                case "scatter": {
                  settings.settings.selectedTitles =
                    settings.settings.selectedTitles.filter((title) =>
                      metricIds.includes(title.toString())
                    );
                  break;
                }
                case "heatmap":
                case "bullet":
                case "indicator": {
                  const findMetric = metricIds.includes(
                    settings.settings.selectedDbMeasureId
                  );
                  settings.settings.selectedDbMeasureId = findMetric
                    ? settings.settings.selectedDbMeasureId
                    : "";
                  break;
                }
                case "sankey": {
                  settings.settings.Label = settings.settings.Label.map(
                    (label) => {
                      if (label.sourceFrom === "Calculated") return label;
                      const { sourceName, sourceAssetMeasure, ...rest } = label;
                      const findMetric = metricIds.includes(sourceName);
                      return {
                        ...rest,
                        sourceName: findMetric ? label.sourceName : "",
                        sourceLabel: findMetric
                          ? metricsIdsNames[label.sourceName] ?? ""
                          : "",
                        sourceAssetMeasure: {
                          ...sourceAssetMeasure,
                          measureId: findMetric ? label.sourceName : "",
                        },
                      };
                    }
                  );
                }
                default:
                  break;
              }
            }
            default:
              break;
          }
          return widget;
        })
      );
      dashboardTemplate.data = JSON.stringify({
        ...dashboardData,
        widget: {
          ...dashboardData.widget,
          widgets: updatedWidgets,
        },
      });
      await this.dashboardTemplates.persistAndFlush(dashboardTemplate);
      return {
        success: true,
        id: dashboardTemplate.id,
        title: dashboardTemplate.title,
        updatedWidgets,
      };
    } catch (error) {
      console.error("Error updating dashboard template widgets:", error);
      throw error;
    }
  }
}
