import {
  EntityManager as Em<PERSON>anager,
  EntityRepository,
  FilterQuery,
  Reference,
} from "@mikro-orm/core";
import { ObjectQuery } from "@mikro-orm/core/typings";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Enti<PERSON><PERSON><PERSON>ger, SqlEntityManager } from "@mikro-orm/postgresql";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { catchError, firstValueFrom } from "rxjs";
import { Alerts } from "src/alert/domain/alert.entity";
import { Asset, AssetId } from "src/assets/domain/asset.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { InvalidInputException } from "src/errors/exceptions";
import { User, UserId } from "src/users/domain/user.entity";
import { AssetMeasurement } from "../domain/asset-measurement.entity";
import { Measurement } from "../domain/measurement.entity";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";

export class AssetMeasurementRepository {
  private alertScheduleURL: string;
  constructor(
    @InjectRepository(AssetMeasurement)
    private readonly entityRepository: EntityRepository<AssetMeasurement>,
    private readonly entityManager: EntityManager,
    private readonly transactionFactory: TransactionFactory,
    private readonly httpService: HttpService,
    private configService: ConfigService
  ) {}
  async onModuleInit(): Promise<void> {
    let url = this.configService.get("alert_scheduler_endpoint");
    this.alertScheduleURL = url;
  }

  async add(
    assetMeasurement: AssetMeasurement,
    createdById?: UserId
  ): Promise<AssetMeasurement> {
    if (createdById) {
      assetMeasurement.createdById = createdById;
      assetMeasurement.measurement.createdById = createdById;
    }

    await this.entityRepository.persistAndFlush(assetMeasurement);

    return assetMeasurement;
  }

  async getAllByAssetId(assetId: number) {
    const measurements = await this.entityRepository.find(
      {
        assetId,
        deletedAt: null,
        measurement: { $or: [{ enabled: true }, { enabled: null }] },
      },
      { orderBy: { measurement: { tag: "ASC" } } }
    );
    return measurements.filter((measure) => measure.datasourceId !== 2);
  }

  async getAllByAssetIdsMeasureIds({
    customerId,
    data,
  }: {
    customerId: number;
    data: { assetId: number; measurementId: number[] }[];
  }) {
    const assetIds = data.map((item) => item.assetId);
    const measurementIds = data.flatMap((item) => item.measurementId);
    if (assetIds.length === 0 || measurementIds.length === 0) {
      return [];
    }
    const assetMeasures = await this.entityRepository.find(
      {
        $and: [
          {
            asset: {
              id: { $in: assetIds },
              deletedAt: null,
              customer: { id: customerId },
              $or: [{ enabled: true }, { enabled: null }],
            },
          },
        ],
      },
      {
        populate: ["typeId", "measurement.unitOfMeasure"],
        orderBy: { measurement: { tag: "ASC" } },
      }
    );
    return assetMeasures
      .filter(
        (measure) =>
          measure.datasourceId !== 2 &&
          measure.id &&
          measurementIds.includes(measure.id) &&
          measure.measurementId
      )
      .map((measure) => {
        const measureData = JSON.parse(JSON.stringify(measure));
        return {
          ...measureData,
          unitOfMeasure: measure.measurement.unitOfMeasure,
          unitOfMeasureId: measure.measurement.unitOfMeasureId,
        };
      });
  }
  async getMeasurementsByIds(measurements: number[]) {
    return await this.entityRepository.find(
      {
        id: {
          $in: measurements,
        },
      },
      { orderBy: { measurement: { tag: "ASC" } } }
    );
  }
  async countByAssetId(assetId: number, filter: { metricId?: number } = {}) {
    const whereFilter: FilterQuery<AssetMeasurement> = {
      asset: {
        id: assetId,
        deletedAt: null,
        $or: [{ enabled: true }, { enabled: null }],
      },
    };

    if (filter.metricId !== undefined) {
      whereFilter.measurement = {
        metric: { id: filter.metricId },
      };
    }

    return await this.entityRepository.count(whereFilter);
  }

  async findById(
    assetMeasurementId: number,
    filter: { assetId?: number; customerId?: number } = {}
  ): Promise<AssetMeasurement | null> {
    let assetFilter: { asset: ObjectQuery<Asset> } | undefined;

    if (filter.assetId) {
      assetFilter = {
        asset: {
          id: filter.assetId,
          deletedAt: null,
          $or: [{ enabled: true }, { enabled: null }],
        },
      };
    }

    if (filter.customerId) {
      assetFilter = assetFilter ?? { asset: {} };
      assetFilter.asset.customer = { id: filter.customerId };
    }

    const whereFilter: FilterQuery<AssetMeasurement> = {
      id: assetMeasurementId,
      deletedAt: null,
      measurement: { $or: [{ enabled: true }, { enabled: null }] },
      ...assetFilter,
    };

    return await this.entityRepository.findOne(whereFilter);
  }
  async findMeasurementById(
    measurementId: number
  ): Promise<AssetMeasurement | null> {
    return await this.entityManager.findOne(AssetMeasurement, {
      measurement: { id: measurementId },
    });
  }
  async remove(
    assetMeasurement: AssetMeasurement,
    headers: Request["headers"],
    deletedById?: UserId,
    emManager?: EmManager
  ) {
    return this.transactionFactory.run(async (emLocal) => {
      const em = emManager ?? emLocal;
      const deletedAt = new Date();
      assetMeasurement.deletedAt = deletedAt;

      if (deletedById) {
        assetMeasurement.deletedById = deletedById;
      }

      const measurementLinkedToSingleAsset =
        (await this.entityManager.count(AssetMeasurement, {
          measurement: {
            id: assetMeasurement.measurementId,
            $or: [{ enabled: true }, { enabled: null }],
          },
          deletedAt: null,
        })) === 1;

      if (measurementLinkedToSingleAsset) {
        if (deletedById) {
          assetMeasurement.measurement.deletedById = deletedById;
        }
        assetMeasurement.measurement.deletedAt = deletedAt;
      }

      await this.entityRepository.persistAndFlush(assetMeasurement);
      await this.httpDeleteAlertProxy(
        this.alertScheduleURL +
          "alerts/measurement/" +
          assetMeasurement.measurementId,
        {},
        {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        }
      );
    });
  }

  private async httpDeleteAlertProxy(url: string, body: any, headers?: any) {
    const { data } = await firstValueFrom(
      await this.httpService
        .delete(url, {
          data: body,
          headers: headers,
        })
        .pipe(
          catchError((error) => {
            console.log(url, body, error.message, error.response);
            throw new InvalidInputException("Error deleting measurement");
          })
        )
    );
    console.log(data);
    return data;
  }
  async batchRemoveByAssetId(
    assetIds: AssetId[],
    occuredAt: Date,
    deletedById?: UserId,
    headers?: Request["headers"],
    emManager?: EntityManager
  ) {
    return this.transactionFactory.run(async (emLocal) => {
      const em = emManager ?? emLocal;
      const sqlEm = em as SqlEntityManager;

      // Step 1: Get measurement IDs linked to given assets
      const linkedMeasurementIdsQB = sqlEm
        .createQueryBuilder(AssetMeasurement)
        .select("measurement")
        .where({ assetId: { $in: assetIds }, deletedAt: null });

      const measurementIdsToDeleteQB = sqlEm
        .createQueryBuilder(AssetMeasurement)
        .select(["measurement"])
        .where({
          measurement: { $in: linkedMeasurementIdsQB.getKnexQuery() },
          deletedAt: null,
        })
        .groupBy("measurement")
        .having({ "count(*)": { $eq: 1 } });

      // Step 2: Soft-delete orphan measurements
      await sqlEm
        .createQueryBuilder(Measurement)
        .update({ deletedAt: occuredAt, deletedById })
        .where({
          id: { $in: measurementIdsToDeleteQB.getKnexQuery() },
        })
        .execute();

      // Step 3: Soft-delete asset-measurements
      await sqlEm
        .createQueryBuilder(AssetMeasurement)
        .update({ deletedAt: occuredAt, deletedById })
        .where({
          assetId: { $in: assetIds },
        })
        .execute();

      // Step 4: Soft-delete dashboards linked to the assets
      const dashboards = await sqlEm.find(Dashboard, {
        asset: { id: { $in: assetIds } },
        deletedAt: null,
      });
      if (dashboards?.length > 0) {
        const suffix = `_deleted_${Date.now()}`;
        const maxLength = 50; // Ensure this matches @Property({ length: 50 }) on `title`
        // Prepare updates in memory
        for (const dashboard of dashboards) {
          const originalTitle = dashboard.title ?? "";
          const trimmedTitle = originalTitle.slice(
            0,
            maxLength - suffix.length
          );
          dashboard.title = `${trimmedTitle}${suffix}`;
          dashboard.deletedAt = occuredAt;
          dashboard.deletedBy = deletedById
            ? Reference.createFromPK(User, deletedById)
            : null;
        }
        // Bulk persist and flush all dashboards at once
        await sqlEm.persistAndFlush(dashboards);
      }
      // Step 5: Clean up alerts for measurements of deleted assets
      const alerts = await em.find(Alerts, {
        asset: { id: { $in: assetIds } },
        deletedAt: null,
      });
      if (alerts?.length > 0) {
        for (const alert of alerts) {
          await this.httpDeleteAlertProxy(
            this.alertScheduleURL +
              "alerts/measurement/" +
              alert.measurement.id,
            {},
            {
              "be-csrftoken": headers?.["be-csrftoken"],
              Cookie: headers?.["cookie"],
            }
          );
        }
      }
    });
  }

  async update(assetMeasurement: AssetMeasurement, updatedById: UserId) {
    assetMeasurement.updatedAt = new Date();
    assetMeasurement.updatedBy = Reference.createFromPK(User, updatedById);

    await this.entityRepository.persistAndFlush(assetMeasurement);
  }
}
