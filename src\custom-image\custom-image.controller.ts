import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  UseGuards,
} from "@nestjs/common";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { CustomImageService } from "./custom-image.service";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { User } from "src/users/domain/user.entity";

@Controller({
  path: "customers/:customerId/custom-image",
  version: "0",
})
@UseGuards(JwtAuthGuard, CsrfGuard)
export class CustomImageController {
  constructor(private readonly customerImageService: CustomImageService) {}

  @Post()
  async createCustomerImage(
    @Param("customerId") customerId: string,
    @AuthUser() user: User,
    @Body() data: { image: string }
  ) {
    // Handle "null" string or undefined/empty values as null
    const parsedCustomerId =
      customerId === "null" || !customerId || customerId === "undefined"
        ? null
        : Number(customerId);

    return await this.customerImageService.create({
      customerId: parsedCustomerId,
      logo: data.image,
      user,
    });
  }

  @Get()
  async getCustomerImage(@Param("customerId") customerId: number) {
    const items = await this.customerImageService.getCustomerImage(customerId);
    return {
      total: items.length,
      items,
    };
  }

  @Get("/:imageId")
  async getImageById(
    @Param("customerId") customerId: number,
    @Param("imageId", ParseIntPipe) imageId: number
  ) {
    return await this.customerImageService.getImage(imageId);
  }
}

// Additional controller for global images (not tied to specific customers)
@Controller({
  path: "custom-image/global",
  version: "0",
})
@UseGuards(JwtAuthGuard, CsrfGuard)
export class GlobalCustomImageController {
  constructor(private readonly customerImageService: CustomImageService) {}

  @Get()
  async getGlobalImages() {
    const items = await this.customerImageService.getGlobalImages();
    return {
      total: items.length,
      items,
    };
  }
}
