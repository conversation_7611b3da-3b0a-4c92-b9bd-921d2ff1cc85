import { Migration } from "@mikro-orm/migrations";

export class Migration20250605120153_dashboard_size_issue extends Migration {
  async up(): Promise<void> {
    // Ensure the "data" column is of type text (no size limit)
    this.addSql(
      'alter table "dashboard" alter column "data" type text using ("data"::text);'
    );
    this.addSql(
      'alter table "dashboard_template" alter column "data" type text using ("data"::text);'
    );
  }

  async down(): Promise<void> {
    // Revert the "data" column type back to text (no size limit)
    this.addSql(
      'alter table "dashboard" alter column "data" type text using ("data"::text);'
    );
    this.addSql(
      'alter table "dashboard" add constraint "data_size_check" check (length("data") <= 65536);'
    );
    this.addSql(
      'alter table "dashboard_template" alter column "data" type text using ("data"::text);'
    );
    this.addSql(
      'alter table "dashboard_template" add constraint "data_size_check" check (length("data") <= 65536);'
    );
  }
}
