import { createMikroOrmTestModule } from "src/db/__testing__/factories";
import { AssetTemplate } from "./domain/asset-template.entity";
import { Test } from "@nestjs/testing";
import { AssetTemplateInstanceService } from "./asset-template-instance.service";
import { AssetTemplateService } from "./asset-template.service";
import { User } from "src/users/domain/user.entity";
import { Customer } from "src/customers/domain/customer.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { EntityManager } from "@mikro-orm/core";
import { AssetService } from "src/assets/asset.service";
import { AssetMeasurementService } from "src/measurements/asset-measurement.service";
import { UnitsGroupService } from "../measurements/units-group.service";
import { assetTemplateFactory } from "./__tests__/asset-template.factory";
import { assetFactory } from "src/assets/__tests__/factories";
import { UnitOfMeasure } from "src/measurements/domain/unit-of-measure.entity";
import { assetMeasurementFactory } from "src/measurements/__tests__/factories";
import { Asset } from "src/assets/domain/asset.entity";
import { UnitOfMeasureService } from "src/measurements/unit-of-measure.service";
import { DatasourceService } from "src/measurements/datasource.service";

describe("AssetTemplateInstanceService", () => {
  describe("create instance", () => {
    test("non existing template should throw an exception", async () => {
      const { assetTemplateInstanceService, nonExistingTemplateId } =
        await createAssetTemplateInstanceService();

      expect(
        assetTemplateInstanceService.create(
          nonExistingTemplateId,
          {
            tag: "",
            assetTypeId: 0,
            customerId: 0,
            parentIds: [],
          },
          new Map(),
          2,
          4
        )
      ).rejects.toThrow("Asset template not found");
    });

    test("template with no metric should throw an exception", async () => {
      const {
        assetTemplateInstanceService,
        waterPumpNoMetricTemplateId,
        europeUnitsGroupId,
      } = await createAssetTemplateInstanceService();

      expect(
        assetTemplateInstanceService.create(
          waterPumpNoMetricTemplateId,
          {
            tag: "Water Pump",
            assetTypeId: 0,
            customerId: 0,
            parentIds: [],
          },
          new Map(),
          europeUnitsGroupId,
          4
        )
      ).rejects.toThrow("Cannot create template instance with no metric");
    });

    test("template with override should be applied when creating asset measurement", async () => {
      const {
        assetTemplateInstanceService,
        assetMeasurementServiceMock,
        waterPumpTemplateId,
        waterPumpPowerMetricId,
        europeUnitsGroupId,
      } = await createAssetTemplateInstanceService();

      await assetTemplateInstanceService.create(
        waterPumpTemplateId,
        {
          tag: "Water Pump",
          assetTypeId: 0,
          customerId: 0,
          parentIds: [],
        },
        new Map([[waterPumpPowerMetricId, { tag: "Water Pump Power" }]]),
        europeUnitsGroupId,
        4
      );

      expect(assetMeasurementServiceMock.create.mock.calls[0][0].tag).toBe(
        "Water Pump Power"
      );
    });

    test("template with non existing unit of measure override should throw an exception", async () => {
      const {
        assetTemplateInstanceService,
        waterPumpTemplateId,
        waterPumpPowerMetricId,
        europeUnitsGroupId,
        nonExistingUnitOfMeasureId,
      } = await createAssetTemplateInstanceService();

      expect(
        assetTemplateInstanceService.create(
          waterPumpTemplateId,
          {
            tag: "Water Pump",
            assetTypeId: 0,
            customerId: 0,
            parentIds: [],
          },
          new Map([
            [
              waterPumpPowerMetricId,
              { unitOfMeasureId: nonExistingUnitOfMeasureId },
            ],
          ]),
          europeUnitsGroupId,
          4
        )
      ).rejects.toThrow(
        "Unit of measure for given group and measurement type not found"
      );
    });

    test("template with showAllUnits override and valid unit should use unitOfMeasureService.getById", async () => {
      const {
        assetTemplateInstanceService,
        assetMeasurementServiceMock,
        waterPumpTemplateId,
        waterPumpPowerMetricId,
        europeUnitsGroupId,
        validUnitOfMeasureId,
        unitOfMeasureServiceMock,
      } = await createAssetTemplateInstanceService({
        showAllUnitsUnitExists: true,
      });

      await assetTemplateInstanceService.create(
        waterPumpTemplateId,
        {
          tag: "Water Pump",
          assetTypeId: 0,
          customerId: 0,
          parentIds: [],
        },
        new Map([
          [
            waterPumpPowerMetricId,
            { unitOfMeasureId: validUnitOfMeasureId, showAllUnits: true },
          ],
        ]),
        europeUnitsGroupId,
        4
      );

      expect(unitOfMeasureServiceMock.getById).toHaveBeenCalledWith(
        validUnitOfMeasureId
      );
      expect(
        assetMeasurementServiceMock.create.mock.calls[0][0].unitOfMeasureId
      ).toBe(validUnitOfMeasureId);
    });

    test("template with showAllUnits override and non-existing unit should throw an exception", async () => {
      const {
        assetTemplateInstanceService,
        waterPumpTemplateId,
        waterPumpPowerMetricId,
        europeUnitsGroupId,
        nonExistingUnitOfMeasureId,
      } = await createAssetTemplateInstanceService({
        showAllUnitsUnitExists: false,
      });

      await expect(
        assetTemplateInstanceService.create(
          waterPumpTemplateId,
          {
            tag: "Water Pump",
            assetTypeId: 0,
            customerId: 0,
            parentIds: [],
          },
          new Map([
            [
              waterPumpPowerMetricId,
              {
                unitOfMeasureId: nonExistingUnitOfMeasureId,
                showAllUnits: true,
              },
            ],
          ]),
          europeUnitsGroupId,
          4
        )
      ).rejects.toThrow("Unit of measure not found for given ID");
    });
  });
});

async function createAssetTemplateInstanceService(options?: {
  showAllUnitsUnitExists?: boolean;
}) {
  const nonExistingTemplateId = 404;
  const waterPumpTemplateId = 3;
  const waterPumpNoMetricTemplateId = 5;
  const assetTemplateServiceMock: Pick<
    AssetTemplateService,
    "findByTemplateAndTypeId"
  > = {
    findByTemplateAndTypeId: jest.fn(async (id) => {
      if (id === waterPumpTemplateId) {
        return waterPumpTemplate;
      } else if (id === waterPumpNoMetricTemplateId) {
        const waterPumpNoMetricTemplate =
          assetTemplateFactory.createWaterPumpTemplate(waterPumpTemplateId);
        waterPumpNoMetricTemplate.measurements[0].metric = undefined;
        waterPumpNoMetricTemplate.id = waterPumpNoMetricTemplateId;
        return waterPumpNoMetricTemplate;
      } else {
        return null;
      }
    }),
  };

  const nonExistingUnitsGroupId = 405;
  const nonExistingUnitOfMeasureId = 406;
  const europeUnitsGroupId = 5;
  const unitsGroupServiceMock: Pick<
    UnitsGroupService,
    "findMeasurementTypeDefaultUnit" | "findUnitByIdAndMeasurementType"
  > = {
    findMeasurementTypeDefaultUnit: jest.fn(async (id, _) => {
      if (id === 5) {
        const powerUnitOfMeasure = new UnitOfMeasure();
        powerUnitOfMeasure.id = 33;
        powerUnitOfMeasure.name = "Watts";
        return powerUnitOfMeasure;
      } else {
        return null;
      }
    }),
    findUnitByIdAndMeasurementType: jest.fn(async () => null),
  };

  const assetServiceMock: Pick<AssetService, "create"> = {
    create: jest.fn(async () => assetFactory.createWaterPump(43)),
  };

  const assetMeasurementServiceMock: jest.Mocked<
    Pick<AssetMeasurementService, "create">
  > = {
    create: jest.fn(async (_params, _customerId, _createdById) =>
      assetMeasurementFactory.createPumpPower(43)
    ),
  };

  const transactionFactoryMock: Pick<TransactionFactory, "run"> = {
    run: jest.fn(
      async (transactionBlock) => await transactionBlock({ commit: jest.fn() } as any)
    ),
  };

  const validUnitOfMeasureId = 33;
  const unitOfMeasureServiceMock: any = {
    getById: jest.fn(async (id) => {
      if (options?.showAllUnitsUnitExists && id === validUnitOfMeasureId) {
        const unit = new UnitOfMeasure();
        unit.id = validUnitOfMeasureId;
        unit.name = "Watts";
        return unit;
      }
      return null;
    }),
  };

  const datasourceServiceMock: any = {
    getAll: jest.fn(async () => [{ id: 1, name: "Calculation" }]),
    getById: jest.fn(async (id) => ({ id, name: "Calculation" })),
  };

  // MikroORM repository mocks
  const calculationMetricInstanceRepositoryMock = {
    findOne: jest.fn(),
    find: jest.fn(),
  };
  const calculationMetricInputRepositoryMock = { find: jest.fn() };
  const calculationTemplateRepositoryMock = { findOne: jest.fn() };
  const calculationInstanceRepositoryMock = { persistAndFlush: jest.fn() };
  const measurementRepositoryMock = { findOne: jest.fn() };
  const calculationInputRepositoryMock = { persistAndFlush: jest.fn() };

  const moduleRef = await Test.createTestingModule({
    imports: [createMikroOrmTestModule([User, Customer, AssetTemplate, Asset])],
    providers: [
      { provide: AssetTemplateService, useValue: assetTemplateServiceMock },
      { provide: AssetService, useValue: assetServiceMock },
      {
        provide: AssetMeasurementService,
        useValue: assetMeasurementServiceMock,
      },
      {
        provide: UnitsGroupService,
        useValue: unitsGroupServiceMock,
      },
      { provide: UnitOfMeasureService, useValue: unitOfMeasureServiceMock },
      { provide: DatasourceService, useValue: datasourceServiceMock },
      { provide: TransactionFactory, useValue: transactionFactoryMock },
      {
        provide: "CalculationMetricInstanceRepository",
        useValue: calculationMetricInstanceRepositoryMock,
      },
      {
        provide: "CalculationMetricInputRepository",
        useValue: calculationMetricInputRepositoryMock,
      },
      {
        provide: "CalculationTemplateRepository",
        useValue: calculationTemplateRepositoryMock,
      },
      {
        provide: "CalculationInstanceRepository",
        useValue: calculationInstanceRepositoryMock,
      },
      { provide: "MeasurementRepository", useValue: measurementRepositoryMock },
      {
        provide: "CalculationInputRepository",
        useValue: calculationInputRepositoryMock,
      },
      AssetTemplateInstanceService,
    ],
  }).compile();

  const waterPumpTemplate =
    assetTemplateFactory.createWaterPumpTemplate(waterPumpTemplateId);
  const waterPumpPowerMetricId =
    waterPumpTemplate.measurements[0].metric?.id ?? 0;

  const assetTemplateInstanceService = moduleRef.get(
    AssetTemplateInstanceService
  );
  return {
    assetTemplateInstanceService,
    assetMeasurementServiceMock,
    nonExistingTemplateId,
    nonExistingUnitsGroupId,
    nonExistingUnitOfMeasureId,
    europeUnitsGroupId,
    waterPumpTemplateId,
    waterPumpNoMetricTemplateId,
    waterPumpPowerMetricId,
    validUnitOfMeasureId,
    unitOfMeasureServiceMock,
  };
}
