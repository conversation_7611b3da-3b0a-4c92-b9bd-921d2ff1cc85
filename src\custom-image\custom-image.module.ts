import { MikroOrmModule } from "@mikro-orm/nestjs";
import { Module } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { CustomImageController, GlobalCustomImageController } from "./custom-image.controller";
import { CustomImageService } from "./custom-image.service";
import { CustomImage } from "./domain/custom-image.entity";
import { CustomImageRepository } from "./repository/custom-image.repository";
import { CustomersModule } from "src/customers/customers.module";

@Module({
  imports: [
    AuthModule,
    MikroOrmModule.forFeature([CustomImage]),
    CustomersModule,
  ],
  controllers: [CustomImageController, GlobalCustomImageController],
  providers: [CustomImageRepository, CustomImageService],
})
export class CustomImageModule {}
