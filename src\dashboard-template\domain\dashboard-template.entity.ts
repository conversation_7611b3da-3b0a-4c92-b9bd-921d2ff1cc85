import {
  <PERSON><PERSON>ty,
  ManyToOne,
  PrimaryKey,
  Property,
  Unique,
} from "@mikro-orm/core";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { Customer } from "src/customers/domain/customer.entity";
import { User } from "src/users/domain/user.entity";

@Entity()
@Unique({ properties: ["customer", "title"] }) // Ensure unique combination of customer and title
@Unique({ properties: ["title"], options: { where: "customer IS NULL" } }) // Prevent duplicate titles when customer is null
// Ensure unique combination of asset_template and title
export class DashboardTemplate {
  @PrimaryKey()
  id: number;

  @ManyToOne({ entity: () => AssetTemplate, fieldName: "asset_template" })
  asset_template!: AssetTemplate;

  @Property()
  title: string;

  @Property({ type: "text" }) // No length limit, suitable for large data
  data: string;

  @ManyToOne({ entity: () => Customer, fieldName: "customer", nullable: true })
  customer?: Customer;

  @ManyToOne({ entity: () => User, fieldName: "createdby" })
  createdby!: User;

  @ManyToOne({ entity: () => User, fieldName: "updatedby", nullable: true })
  updatedby?: User;

  @Property({ nullable: true })
  createdat?: Date;

  @Property({ nullable: true })
  updatedat?: Date;

  @ManyToOne({ entity: () => User, fieldName: "deleted_by", nullable: true })
  deleted_by!: User;

  @Property({ nullable: true })
  deleted_at?: Date;
}
