# SECURITY_CORS_ORIGIN_URL="https://dev.pivotol.ai"
# DB_HOST="dataloggertest.clclbj3j3ehf.us-east-1.rds.amazonaws.com"
# DB_NAME="production_backup"
# DB_SSL="false"
# X_ACTIVE_CUSTOMER=x-active-customer-id
# DB_USER="postgres"
# DB_PASSWORD="Br0mpt0n!0T"
# FRONTEND_URL="https://dev.pivotol.ai"
# FAST_API="http://timeseries-api-service.application.svc.cluster.local"
# MQTT_URL="mqtt://bromptonenergy.io:8883"
# MQTT_USERNAME=""
# MQTT_PASSWORD=""
# MQTT_TOPIC_ALERT="spBv1.0/Brenes_Gateway/NDATA/#"
# MQTT_TOPIC_NOTIFICATION="be-notification-dev"
# MQTT_CLIENT_ID="beasdf"
# MQTT_ROLE="publisher"
# TS_API_HOST="timeseries-api-service.application.svc.cluster.local"
# TS_API_PORT="443"
# TS_API_SSL="false"
# TS_API_VERSION="v1_0"
# SEND_NOTIFICATIONS="true"
# TWILLIO_ACCOUNT_SID="**********************************"
# TWILLIO_AUTH_TOKEN="46910f888ea461bcde388a682d069592"
# SMS_FROM_NO="+***********"
# TWILLIO_EMAIL_API_TOKEN="*********************************************************************"
# EMAIL_FROM="<EMAIL>"
# SCHEDULER_ENDPOINT="http://scheduler-api.application.svc.cluster.local/"
# REGION="us-east-1"
# ACCESS_KEY_ID="********************"
# SECRET_ACCESS_KEY="hyBYiQYDuRnIyxTsVYpwXBb6Z1baoSZQeZe1iqdV"
# KAFKA_BROKER="strimzi-cluster-kafka-brokers.kafka-strimzi.svc.cluster.local:9092"
# KAFKA_CLIENT_ID="nestjs-client"
# KAFKA_TOPIC_ALERT="be-alert-dev-new"
# KAFKA_TOPIC_NOTIFICATION="be-notification-dev-new"
# KAFKA_GROUP_ID="nestjs-consumer"
# KAFKA_GROUP_ID_INSTANCE="nestjs-consumer-instance"
# KAFKA_NOTIFICATION_GROUP_ID="notification-consumer"
# KAFKA_NOTIFICATIONP_ID_INSTANCE="notification-consumer-instance"
# NODE_TLS_REJECT_UNAUTHORIZED="0"
# TEMPEST_WEATHER_API_TOKEN="16f1648e-fd77-4fd4-ba09-b711e7de8733"
# TEMPEST_WEATHER_API_URL="https://swd.weatherflow.com/swd/rest"
# AUTH_COOKIE_DOMAIN="pivotol.ai"
# AUTH_JWT_SECRET="this is a very secret secret"
# AUTH_SESSION_DURATION_MIN="43200"