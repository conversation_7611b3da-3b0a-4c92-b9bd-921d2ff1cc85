import { ApiProperty } from "@nestjs/swagger";

export class DashboardItem {
  @ApiProperty({ required: true, example: 1 })
  id: number;

  @ApiProperty({ required: true, example: "dashboard-123" })
  title: string;

  @ApiProperty({ required: true, example: true })
  default: boolean;

  @ApiProperty({ required: true, example: true })
  favorite: boolean;
}

export class GetAllDashboardSuccessResDto {
  @ApiProperty({ required: true, type: [DashboardItem] })
  items: Array<DashboardItem>;

  @ApiProperty({ required: true, example: 1 })
  total: number;

  @ApiProperty({ required: false, example: 755, nullable: true })
  customerDefaultDashboardId?: number | null;

  @ApiProperty({ required: false, type: [Number], example: [750, 747] })
  customerFavoriteDashboardIds?: number[];
}

export class GetAllDashboardForbiddenResDto {
  @ApiProperty({ required: true, example: 403 })
  statusCode: number;

  @ApiProperty({ required: true, example: "Forbidden" })
  error: string;
}
