import { EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { CalculationInput } from "../domain/calculation-input.entity";
import { CalculationInstance } from "../domain/calculation-instance.entity";
import { CalculationPeriod } from "../domain/calculation-period.entity";
import { CalculationTemplate } from "../domain/calculation-template.entity";
import {
  CalcGetAllInstanceDto,
  CalcInstanceDto,
} from "../dto/calc-inputs-create.dto";
import { User } from "src/users/domain/user.entity";
import {
  ConflictException,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { EntityManager as emTranscation } from "@mikro-orm/core";

import { Measurement } from "src/measurements/domain/measurement.entity";
import { EntityManager } from "@mikro-orm/postgresql";
import { Customer, CustomerId } from "src/customers/domain/customer.entity";
import { DataType } from "src/measurements/domain/data-type.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { TimeSeriesService } from "src/measurements/time-series.service";
import { AssetMeasurementRepository } from "src/measurements/repository/asset-measurement.repository";
import { InvalidInputException } from "src/errors/exceptions";

export class CalcEngineRepository {
  constructor(
    @InjectRepository(CalculationInput)
    private calcInputRepository: EntityRepository<CalculationInput>,
    @InjectRepository(CalculationInstance)
    private calcInstanceRepository: EntityRepository<CalculationInstance>,
    @InjectRepository(CalculationPeriod)
    private calcPeriodRepository: EntityRepository<CalculationPeriod>,
    @InjectRepository(Measurement)
    private measurementRepository: EntityRepository<Measurement>,
    @InjectRepository(CalculationTemplate)
    private calcTemplateRepository: EntityRepository<CalculationTemplate>,
    @InjectRepository(DataType)
    private readonly dataTypeRepository: EntityRepository<DataType>,
    private readonly transactionFactory: TransactionFactory,
    private readonly timeSeriesService: TimeSeriesService,
    private readonly em: EntityManager,
    private readonly logger: Logger,
    private readonly assetMeasurementRepository: AssetMeasurementRepository
  ) {}

  public async createCalcInputs(
    constantInputs: CalcInstanceDto,
    authUser: User,
    headers: Request["headers"],
    customerId: CustomerId,
    em?: emTranscation
  ) {
    try {
      return await this.transactionFactory.run(async (emanager) => {
        const entityManager = em ?? emanager;
        const findAndCheckCalcInstance =
          await this.calcInstanceRepository.findOne({
            outputMeasurement: constantInputs.outputMesurementId,
          });
        const measure = constantInputs.inputs.find(
          (input) => input?.measurementId !== undefined
        );
        if (!measure) {
          throw new ConflictException("Atleast one input should be mesurement");
        }
        if (findAndCheckCalcInstance) {
          throw new ConflictException("Output Mesurement already exist");
        }

        const calcInstance = this.calcInstanceRepository.create({
          calculation: constantInputs.templateId,
          outputMeasurement: constantInputs.outputMesurementId,
          ispersisted: constantInputs.ispersisted,
          pollPeriod: constantInputs.ispersisted
            ? constantInputs.pollPeriod
            : null,
          created: new Date(),
          createdby: authUser.id,
        });
        await this.calcInstanceRepository.persistAndFlush(calcInstance);
        if (calcInstance.ispersisted) {
          const assetMeasure =
            await this.assetMeasurementRepository.findMeasurementById(
              constantInputs.outputMesurementId
            );
          if (!assetMeasure) {
            throw new NotFoundException("Asset Measurement not found");
          }
          if (constantInputs.iswriteback) {
            assetMeasure.measurement.writeback = constantInputs.iswriteback;
          } else {
            assetMeasure.measurement.writeback = false;
          }
          await this.assetMeasurementRepository.update(
            assetMeasure,
            authUser.id
          );
          await this.timeSeriesService.create(
            assetMeasure,
            customerId,
            headers,
            calcInstance.ispersisted,
            constantInputs.iswriteback
          );
        }
        if (calcInstance) {
          let isError = false;
          const allInputs = [];
          for (let i = 0; i < constantInputs.inputs.length; i++) {
            const calcInput = this.calcInputRepository.create({
              created: new Date(),
              createdby: authUser.id,
              calculationInstance: calcInstance.id,
            });

            calcInput.inputLabel = constantInputs.inputs[i].inputLabel;
            calcInput.comment = constantInputs.inputs[i].comment ?? "";

            if (constantInputs.inputs[i].measurementId) {
              const checkMesurement = await this.measurementRepository.findOne({
                id: constantInputs.inputs[i].measurementId,
              });
              if (!checkMesurement) {
                isError = true;
                break;
              }
              calcInput.measurement = checkMesurement ?? null;
            } else {
              calcInput.constantNumber =
                constantInputs.inputs[i].constantType === "number"
                  ? constantInputs.inputs[i].constantValue
                  : null;
              calcInput.constantString =
                constantInputs.inputs[i].constantType === "string"
                  ? constantInputs.inputs[i].constantValue
                  : null;
            }
            this.calcInputRepository.persistAndFlush(calcInput);
            allInputs.push(calcInput);
          }

          if (isError) {
            throw new ConflictException("Mesurement not found");
          }

          return allInputs;
        }
      });
    } catch (error: any) {
      this.logger.error(
        `Error creating calculation inputs: ${JSON.stringify(error)}`
      );
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof InvalidInputException) {
        throw error;
      }
      throw new InternalServerErrorException(
        "Something Went's Wrong",
        error.message
      );
    }
  }

  public async findAllCalcInstances(
    customerId: CustomerId,
    calcInstanceDto: CalcGetAllInstanceDto
  ) {
    let data_type_query = ``;
    let data_type_dto = Number(calcInstanceDto.data_type);
    if (data_type_dto) {
      data_type_query = `
        AND ci.output_measurement IN (
          SELECT id FROM measurement WHERE data_type = ${data_type_dto}
        )
      `;
    }
    const query = `
      SELECT ci.*,
      json_agg(json_build_object(
        'id', ci2.id,
        'input_label', ci2.input_label,
        'created', ci2.created,
        'updated', ci2.updated,
        'createdby', ci2.createdby,
        'updatedby', ci2.updatedby,
        'calculation_instance', ci2.calculation_instance,
        'measurement', (SELECT json_build_object(
          'id', m.id,
          'tag', m.tag,
          'data_type', m.data_type,
          'unit_of_measure', m.unit_of_measure
        ) FROM (SELECT DISTINCT id, tag, data_type, unit_of_measure FROM measurement) m where m.id = ci2.measurement),
        'constant_number', ci2.constant_number,
        'constant_string', ci2.constant_string,
        'comment', ci2.comment
      )) AS calculation_inputs,
      (SELECT json_build_object(
        'id', m.id,
        'tag', m.tag,
        'data_type', m.data_type,
        'unit_of_measure', m.unit_of_measure
      ) FROM (SELECT DISTINCT id, tag, data_type, unit_of_measure FROM measurement) m where m.id = ci.output_measurement) AS output_measurement
      FROM calculation_instance ci
      JOIN calculation_input ci2 ON ci.id = ci2.calculation_instance
      WHERE ci.customer = ${customerId}
      ${data_type_query}
      GROUP BY ci.id, ci.output_measurement;
    `;

    const result = await this.em.getConnection().execute(query);
    return result;
  }

  public async updateCalcInputs(
    instanceId: number,
    constantInputs: CalcInstanceDto,
    authUser: User,
    cust_id: CustomerId,
    headers: Request["headers"],
    em?: emTranscation
  ) {
    try {
      return await this.transactionFactory.run(async (emanager) => {
        const entityManager = em ?? emanager;
        const measure = constantInputs.inputs.find(
          (input) => input?.measurementId !== undefined
        );
        if (!measure) {
          throw new ConflictException("Atleast one input should be mesurement");
        }
        const getAllInputsMesurement = (constantInputs.inputs ?? [])
          .map((item) => item.measurementId)
          .filter(
            (measurementId) =>
              measurementId !== undefined && measurementId !== null
          );

        getAllInputsMesurement.push(constantInputs.outputMesurementId);

        const [calcInstance, measurements, calc_template, calcInputs] =
          await Promise.all([
            this.calcInstanceRepository.findOne({
              id: instanceId,
            }),
            this.measurementRepository.find({
              id: { $in: getAllInputsMesurement },
            }),
            this.calcTemplateRepository.findOne({
              id: constantInputs.templateId,
            }),
            this.calcInputRepository.find({
              id: { $in: constantInputs.inputs.map((input) => input.inputId) },
            }),
          ]);

        if (!calcInstance) {
          throw new NotFoundException(
            "Calculation Instance not found" + instanceId
          );
        }

        if (!calc_template) {
          throw new NotFoundException(
            "Calculation Template not found" + instanceId
          );
        }

        calcInstance.updated = new Date();
        calcInstance.outputMeasurement = calcInstance.outputMeasurement;
        calcInstance.updatedby = authUser.id;
        calcInstance.ispersisted = constantInputs.ispersisted;
        calcInstance.pollPeriod = constantInputs.ispersisted
          ? constantInputs.pollPeriod
          : null;
        calcInstance.calculation = calc_template;
        // this.calcInstanceRepository.persistAndFlush(calcInstance);
        await this.calcInstanceRepository.nativeUpdate(
          {
            id: calcInstance.id,
          },
          {
            ...calcInstance,
          }
        );
        if (calcInstance.ispersisted) {
          const assetMeasure =
            await this.assetMeasurementRepository.findMeasurementById(
              constantInputs.outputMesurementId
            );
          if (!assetMeasure) {
            throw new NotFoundException("Asset Measurement not found");
          }
          if (constantInputs.iswriteback) {
            assetMeasure.measurement.writeback = constantInputs.iswriteback;
          } else {
            assetMeasure.measurement.writeback = false;
          }
          await this.assetMeasurementRepository.update(
            assetMeasure,
            authUser.id
          );
          await this.timeSeriesService.update(assetMeasure, cust_id, headers);
        }
        await Promise.all(
          constantInputs.inputs.map(async (input, index) => {
            const calcInput = calcInputs.find(
              (inputs) => inputs.id === input.inputId
            );
            if (calcInput) {
              const { id, ...calcInputData } = calcInput;
              const updatePayload = {
                ...calcInputData,
              };
              updatePayload.inputLabel = input.inputLabel;
              updatePayload.comment = input.comment ?? "";

              if (input.measurementId) {
                updatePayload.measurement =
                  measurements.find(
                    (item) => item.id === input.measurementId
                  ) ?? null;
                updatePayload.constantNumber = null;
                updatePayload.constantString = null;
              } else {
                updatePayload.constantNumber =
                  input.constantType === "number" ? input.constantValue : null;
                updatePayload.constantString =
                  input.constantType === "string" ? input.constantValue : null;
                updatePayload.measurement = null;
              }
              await this.calcInputRepository.nativeUpdate(
                {
                  id: id,
                },
                {
                  ...updatePayload,
                }
              );
            }
          })
        );
      });
    } catch (error) {
      this.logger.error(
        `Error updating calculation inputs for instance ID ${instanceId}: ${JSON.stringify(
          error
        )}}`
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof InvalidInputException) {
        throw error;
      }
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      throw new InternalServerErrorException("Something Went Wrong");
    }
  }

  public async findAllCalculationTemplates() {
    return this.calcTemplateRepository.find({});
  }

  public async findCalcTemplateByName(name: string) {
    const foundTemplate = await this.calcTemplateRepository.findOne({
      name: name,
    });
    if (foundTemplate) {
      throw new ConflictException(
        `Calculation Template already exist with "${foundTemplate.name}" name.`
      );
    }
    return false;
  }

  public async updateCalcTemplate(
    templateId: number,
    expressionTemplate: CalculationTemplate,
    authUser: User
  ) {
    const expression = await this.calcTemplateRepository.findOne({
      id: templateId,
    });

    if (!expression) {
      throw new NotFoundException("Calculation Template not found");
    }
    expression.expression = expressionTemplate.expression;
    expression.updated = new Date();
    expression.name = expressionTemplate.name;
    expression.description = expressionTemplate.description;
    expression.updatedby = authUser.id;
    expression.dataType = expressionTemplate.dataType;
    await this.calcTemplateRepository.persistAndFlush(expression);
    return expression;
  }

  public async createCalcTemplate(
    expressionTemplate: CalculationTemplate,
    authUser: User
  ) {
    const expression = await this.calcTemplateRepository.create({
      ...expressionTemplate,
      created: new Date(),
      updated: new Date(),
      createdby: authUser.id,
      updatedby: authUser.id,
    });
    await this.calcTemplateRepository.persistAndFlush(expression);
    return expression;
  }

  public async getAllDataTypes() {
    return this.dataTypeRepository.findAll();
  }

  public async findAllCalculationTimePeriods() {
    return this.calcPeriodRepository.find({});
  }

  public async findCalcInstanceByMesurementId(measurementId: number) {
    return this.calcInstanceRepository.findOne({
      outputMeasurement: measurementId,
    });
  }

  public async findCalcTemplateById(templateId: number) {
    return this.calcTemplateRepository.findOne({
      id: templateId,
    });
  }

  public async findCalcInputsByCalcInstancetId(calcInstacenId: number) {
    const calcInputRepositories = await this.calcInputRepository.find(
      {
        calculationInstance: calcInstacenId,
      },
      {
        orderBy: { id: "ASC" },
      }
    );
    const calcInputs = Promise.all(
      calcInputRepositories.map(async (calcInput) => {
        const measure = await this.measurementRepository.findOne({
          id: calcInput.measurement?.id,
        });
        return {
          ...calcInput,
          measurement: measure ? measure?.tag : "",
          measurementId: measure ? measure?.id : 0,
        };
      })
    );
    return calcInputs;
  }
}
