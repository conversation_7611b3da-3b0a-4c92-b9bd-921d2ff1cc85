import {
  <PERSON><PERSON>ty,
  LoadStrategy,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryKey,
  Property,
  Ref,
} from "@mikro-orm/core";
import { User, UserId } from "src/users/domain/user.entity";
import { Asset } from "../../assets/domain/asset.entity";
import { Measurement } from "../../measurements/domain/measurement.entity";
import { Aggregates } from "./aggregates.entity";
import { AlertCondition } from "./alertCondition.entity";
import { AlertThresholdType } from "./alertThresholdType.entity";
import { AlertUsers } from "./alertUser.entity";
import { AnomalyModel } from "./anomalyModel.entity";
import { AnomalyParameter } from "./anomalyParameter.entity";
import { Periods } from "./periods.entity";

@Entity()
export class Alerts {
  @PrimaryKey()
  id!: number;

  @ManyToOne({ entity: () => Asset })
  asset!: Asset;

  @ManyToOne({ entity: () => Measurement })
  measurement!: Measurement;

  @ManyToOne({ entity: () => Aggregates, fieldName: "agg" })
  agg!: Aggregates;

  @ManyToOne({ entity: () => Periods, fieldName: "period", nullable: true })
  period!: Periods;

  @ManyToOne({ entity: () => AlertThresholdType, fieldName: "threshold_type" })
  thresholdType!: AlertThresholdType;

  @ManyToOne({
    entity: () => AlertCondition,
    fieldName: "condition",
    nullable: true,
  })
  condition?: AlertCondition | null;

  @Property({ nullable: true })
  thresholdValue?: number | null;

  @Property({ nullable: true })
  resetDeadband?: number | null;

  @Property({ nullable: true })
  staleBand?: number | null;

  @Property({ length: 150, nullable: true })
  description?: string;

  @Property({ nullable: true })
  notificationType?: number;

  @Property({ nullable: true })
  customerId?: number;

  @Property({ nullable: true })
  enabled: boolean;

  @OneToMany({
    entity: () => AlertUsers,
    mappedBy: "alert",
    strategy: LoadStrategy.JOINED,
  })
  alertUsers?: AlertUsers[];

  @Property({ nullable: true })
  state?: number;

  @OneToOne(() => AnomalyModel, (anomalyModel) => anomalyModel.alert, {
    nullable: true,
    orphanRemoval: true,
    mappedBy: "alert",
  })
  anomalyModel?: AnomalyModel;

  @OneToOne(
    () => AnomalyParameter,
    (anomalyParameter) => anomalyParameter.alertRelation,
    {
      nullable: true,
      orphanRemoval: true,
    }
  )
  anomalyParameter?: AnomalyParameter;

  @Property({ length: 6, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, nullable: true })
  updatedAt?: Date;

  @Property({ hidden: true, length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "deleted_by",
  })
  deletedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "deleted_by" })
  deletedById?: UserId;

  @Property({ nullable: true })
  lastProcessedTs?: number;
}
