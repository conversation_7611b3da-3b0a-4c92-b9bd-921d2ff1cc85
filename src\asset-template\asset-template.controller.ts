import {
  Body,
  Controller,
  Get,
  Inject,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { ApiCreatedResponse, ApiOkResponse } from "@nestjs/swagger";
import authConfiguration from "src/authentication/auth.config";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { collectionSchema } from "src/serialization/collection.dto";
import { User } from "src/users/domain/user.entity";
import { AssetTemplateService } from "./asset-template.service";
import {
  AssetTemplateCreationDto,
  AssetTemplateDto,
  AssetTemplateEditDto,
  expressionInstanceDto,
} from "./dto/asset-template.dto";
import { AssetTemplateMapper } from "./dto/asset-template.mapper";

@Controller({
  version: "0",
  path: "assets-backoffice/asset-types/:assetTypeId/asset-templates",
})
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetTemplateApiController {
  constructor(
    private readonly assetTemplateService: AssetTemplateService,
    private readonly assetTemplateMapper: AssetTemplateMapper,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Post()
  @ApiCreatedResponse({ type: AssetTemplateDto })
  @HasRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Param("assetTypeId") assetTypeId: string,
    @Body() newTemplate: AssetTemplateCreationDto,
    @CookieToken() headers: Request["headers"]
  ): Promise<AssetTemplateDto> {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    const {
      manufacturer,
      model_number,
      measurements,
      save_as_global_asset_template,
    } = newTemplate;

    const newAssetTemplate = await this.assetTemplateService.create(
      {
        manufacturer,
        modelNumber: model_number,
        assetTypeId: Number(assetTypeId),
        save_as_global_asset_template,
        measurements: measurements.map((measurementDto) => {
          const {
            metric_id,
            type_id,
            data_type_id,
            value_type_id,
            location_id,
            datasource_id,
            description,
            meter_factor,
          } = measurementDto;
          return {
            metricId: metric_id,
            measurementTypeId: type_id,
            dataTypeId: data_type_id,
            valueTypeId: value_type_id,
            locationId: location_id,
            datasourceId: datasource_id,
            description: description,
            meterFactor: meter_factor,
          };
        }),
      },
      authUser.id,
      customerId
    );

    return this.assetTemplateMapper.toDto(newAssetTemplate);
  }

  @Post("multi")
  @ApiCreatedResponse({ type: AssetTemplateDto })
  @HasRole(Role.POWER_USER)
  async createMulti(
    @AuthUser() authUser: User,
    @Param("assetTypeId") assetTypeId: string,
    @Body()
    body: {
      metrics: string[];
      newTemplate: AssetTemplateCreationDto;
      expressionInstance?: expressionInstanceDto[];
    },
    @CookieToken() headers: Request["headers"] //  Promise<AssetTemplateDto> {
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    const {
      manufacturer,
      model_number,
      measurements,
      save_as_global_asset_template,
    } = body.newTemplate;

    const newAssetTemplate = await this.assetTemplateService.createMulti(
      body.metrics,
      {
        manufacturer,
        modelNumber: model_number,
        assetTypeId: Number(assetTypeId),
        save_as_global_asset_template,
        measurements: measurements.map((measurementDto) => {
          const {
            metric_id,
            type_id,
            data_type_id,
            value_type_id,
            location_id,
            datasource_id,
            description,
            meter_factor,
          } = measurementDto;
          return {
            metricId: metric_id,
            measurementTypeId: type_id,
            dataTypeId: data_type_id,
            valueTypeId: value_type_id,
            locationId: location_id,
            datasourceId: datasource_id,
            description: description,
            meterFactor: meter_factor,
          };
        }),
      },
      authUser,
      customerId,
      body.expressionInstance
    );
    return this.assetTemplateMapper.toDto(newAssetTemplate);
  }

  @ApiOkResponse(collectionSchema(AssetTemplateDto))
  @Get()
  @HasRole(Role.POWER_USER)
  async getAllByAssetType(
    @Param("assetTypeId") assetTypeId: string,
    @CookieToken() headers: Request["headers"]
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    const items = await this.assetTemplateService.getAllByAssetType(
      Number(assetTypeId),
      customerId
    );

    return {
      items: items.map((template) => this.assetTemplateMapper.toDto(template)),
      total: items.length,
    };
  }

  @Get("/:assetTemplateId")
  @HasRole(Role.POWER_USER)
  async getAssetTemplateById(
    @Query("assetAffected") assetAffected: boolean,
    @Param() params: { assetTypeId: number; assetTemplateId: number }
  ) {
    const assetTemplate = await this.assetTemplateService.getAssetTemplate(
      params.assetTypeId,
      params.assetTemplateId
    );
    if (assetAffected) {
      let assets = await this.assetTemplateService.getAssociatedAssets(
        params.assetTemplateId
      );
      return {
        ...this.assetTemplateMapper.toDto(assetTemplate.assetTemplate, assets),
        calculatedMetrics: assetTemplate.calculatedMetrics,
        calculatedMetricsInputs: assetTemplate.calculatedMetricsInputs,
      };
    }
    return {
      ...this.assetTemplateMapper.toDto(assetTemplate.assetTemplate),
      calculatedMetrics: assetTemplate.calculatedMetrics,
      calculatedMetricsInputs: assetTemplate.calculatedMetricsInputs,
    };
  }

  @Patch("/:assetTemplateId")
  @HasRole(Role.POWER_USER)
  async updateAssetTemplate(
    @Param() params: { assetTypeId: number; assetTemplateId: number },
    @Body() updateAssetTemplate: AssetTemplateEditDto,
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"]
  ) {
    return await this.assetTemplateService.editAssetTemplate(
      params.assetTypeId,
      params.assetTemplateId,
      {
        manufacturer: updateAssetTemplate.manufacturer,
        modelNumber: updateAssetTemplate.model_number,
        assetTypeId: Number(updateAssetTemplate.asset_type_id),
        measurements: updateAssetTemplate.measurements.map((measurementDto) => {
          const {
            metric_id,
            type_id,
            data_type_id,
            value_type_id,
            location_id,
            datasource_id,
            description,
            meter_factor,
            id,
          } = measurementDto;
          return {
            metricId: metric_id,
            measurementTypeId: type_id,
            dataTypeId: data_type_id,
            valueTypeId: value_type_id,
            locationId: location_id,
            datasourceId: datasource_id,
            description: description,
            meterFactor: meter_factor,
            id: id,
          };
        }),
      },
      authUser.id,
      headers
    );
  }

  @Patch("/:assetTemplateId/multi")
  @HasRole(Role.POWER_USER)
  async updateAssetTemplateMulti(
    @Param() params: { assetTypeId: number; assetTemplateId: number },
    @Body()
    body: {
      metrics: string[];
      updateAssetTemplate: AssetTemplateEditDto;
      expressionInstance?: expressionInstanceDto[];
    },
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"]
  ) {
    return await this.assetTemplateService.editAssetTemplateMulti(
      body.metrics,
      params.assetTypeId,
      params.assetTemplateId,
      {
        manufacturer: body.updateAssetTemplate.manufacturer,
        modelNumber: body.updateAssetTemplate.model_number,
        assetTypeId: Number(body.updateAssetTemplate.asset_type_id),
        measurements: body.updateAssetTemplate.measurements.map(
          (measurementDto) => {
            const {
              metric_id,
              type_id,
              data_type_id,
              value_type_id,
              location_id,
              datasource_id,
              description,
              meter_factor,
              id,
            } = measurementDto;
            return {
              metricId: metric_id,
              measurementTypeId: type_id,
              dataTypeId: data_type_id,
              valueTypeId: value_type_id,
              locationId: location_id,
              datasourceId: datasource_id,
              description: description,
              meterFactor: meter_factor,
              id: id,
            };
          }
        ),
      },
      authUser,
      headers,
      undefined,
      body.expressionInstance
    );
  }
  @Put("/:assetTemplateId")
  @HasRole(Role.POWER_USER)
  async cloneAssetTemplateToCustomer(
    @Param() params: { assetTypeId: number; assetTemplateId: number },
    @CookieToken() headers: Request["headers"],
    @AuthUser() authUser: User,
    @Body() body: { manufacturer: string; model_number: string }
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    const assetTemplate = await this.assetTemplateService.getAssetTemplate(
      params.assetTypeId,
      params.assetTemplateId
    );
    if (!assetTemplate) {
      throw new NotFoundException("Asset template not found");
    }
    const clonedAssetTemplate = await this.assetTemplateService.create(
      {
        assetTypeId: assetTemplate.assetTemplate.assetType.id,
        manufacturer: body.manufacturer,
        modelNumber: body.model_number,
        save_as_global_asset_template: false,
        measurements: assetTemplate.assetTemplate.measurements.map(
          (measurement) => {
            return {
              dataTypeId: measurement.dataType.id,
              measurementTypeId: measurement.measurementType.id,
              metricId: measurement.metric.id,
              valueTypeId: measurement.valueType.id,
              datasourceId: measurement.datasource?.id,
              description: measurement.description,
              locationId: measurement.location?.id,
              meterFactor: measurement.meterFactor,
            };
          }
        ),
      },
      authUser.id,
      customerId
    );
    return this.assetTemplateMapper.toDto(clonedAssetTemplate);
  }
}
