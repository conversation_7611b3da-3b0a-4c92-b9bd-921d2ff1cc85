import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import { ApiCreatedResponse, ApiOkResponse, ApiQuery } from "@nestjs/swagger";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasCustomerRole } from "src/authorization/infra/roles.decorator";
import { RolesGuard } from "src/authorization/infra/roles.guard";
import { collectionSchema } from "src/serialization/collection.dto";
import { User } from "src/users/domain/user.entity";
import { AssetService } from "./asset.service";
import { AssetCreationDto, AssetDto, AssetUpdateDto } from "./dto/asset.dto";
import { mapQueryParamArray } from "./mappers";

@Controller({ version: "0", path: "customers/:customerId/assets" })
@UseGuards(JwtAuthGuard, CsrfGuard, RolesGuard)
export class AssetApiController {
  constructor(private readonly assetService: AssetService) {}

  @Post()
  @ApiCreatedResponse({ type: AssetDto })
  @HasCustomerRole(Role.POWER_USER)
  async create(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Body() newAsset: AssetCreationDto
  ) {
    return await this.assetService.create(
      {
        ...newAsset,
        parentIds: newAsset.parent_ids,
        assetTypeId: newAsset.type_id,
        customerId: Number(customerId),
        timeZone: newAsset.time_zone,
      },
      authUser.id
    );
  }

  @Get(":id")
  @ApiOkResponse({ type: AssetDto })
  @HasCustomerRole(Role.USER)
  async getById(
    @AuthUser() authUser: User,
    @Param("id") id: string,
    @Param("customerId") customerId: string,
    @Query("includeRelated") includeRelated?: string
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }
    const includeRelatedBool =
      includeRelated === "true" || includeRelated === "1";
    const asset = await this.assetService.findById(
      Number(id),
      Number(customerId)
    );

    if (asset === null) {
      throw new NotFoundException("Asset not found");
    }
    if (includeRelatedBool) {
      const relatedEntities =
        await this.assetService.findRelatedEntitiesToAsset(
          asset.id,
          Number(customerId)
        );
      if (relatedEntities === null) {
        throw new NotFoundException("Asset not found");
      }
      return {
        total: relatedEntities.assets.length,
        items: relatedEntities.assets,
      };
    }
    return asset;
  }

  @Get()
  @ApiOkResponse(collectionSchema(AssetDto))
  @HasCustomerRole(Role.USER)
  @ApiQuery({
    name: "ids",
    description: "Comma separated list of ids to filter by.",
    required: false,
  })
  @ApiQuery({
    name: "parentIds",
    description:
      "Comma separated list of parent ids to filter by. Root node is -1.",
    required: false,
  })
  async getAll(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Query("ids") ids?: string,
    @Query("parentIds") parentIds?: string
  ) {
    const assetIds = ids
      ? mapQueryParamArray(ids).map((id) => Number(id))
      : undefined;

    const assetParentIds = parentIds
      ? mapQueryParamArray(parentIds).map((id) => Number(id))
      : undefined;

    const items = await this.assetService.getAll({
      customerId: Number(customerId),
      ids: assetIds,
      parentIds: assetParentIds,
    });
    return {
      items: items,
      total: items.length,
    };
  }

  @Delete(":id")
  @HttpCode(204)
  @HasCustomerRole(Role.POWER_USER)
  async removeById(
    @AuthUser() authUser: User,
    @Param("id") id: string,
    @Param("customerId") customerId: string,
    @CookieToken() headers: Request["headers"]
  ) {
    await this.assetService.remove(
      Number(id),
      Number(customerId),
      authUser.id,
      headers
    );
  }

  @Patch(":id")
  @HttpCode(204)
  @HasCustomerRole(Role.POWER_USER)
  async patchById(
    @AuthUser() authUser: User,
    @Param("id") id: string,
    @Param("customerId") customerId: string,
    @Body() assetUpdate: AssetUpdateDto,
    @CookieToken() headers: Request["headers"]
  ) {
    await this.assetService.update(
      Number(customerId),
      Number(id),
      {
        ...assetUpdate,
        timeZone: assetUpdate.time_zone,
        parentIds: assetUpdate.parent_ids,
        assetTypeId: assetUpdate.type_id,
      },
      authUser.id,
      headers
    );
  }
}
