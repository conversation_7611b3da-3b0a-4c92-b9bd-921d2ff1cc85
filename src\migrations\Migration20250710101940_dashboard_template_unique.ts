import { Migration } from "@mikro-orm/migrations";

export class Migration20250710101940_dashboard_template_unique extends Migration {
  async up(): Promise<void> {
    // Add unique constraint for customer + title combination
    this.addSql(
      'alter table "dashboard_template" add constraint "dashboard_template_customer_title_unique" unique ("customer", "title");'
    );

    // Add partial unique index for titles when customer is NULL
    this.addSql(
      'create unique index "dashboard_template_title_null_customer_unique" on "dashboard_template" ("title") where "customer" is null;'
    );
  }

  async down(): Promise<void> {
    // Drop the partial unique index first
    this.addSql('drop index "dashboard_template_title_null_customer_unique";');

    // Drop the unique constraint
    this.addSql(
      'alter table "dashboard_template" drop constraint "dashboard_template_customer_title_unique";'
    );
  }
}
