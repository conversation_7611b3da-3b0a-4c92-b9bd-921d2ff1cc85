{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "aggregates", "schema": "public", "indexes": [{"keyName": "aggregates_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "condition": {"name": "condition", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "alert_condition", "schema": "public", "indexes": [{"keyName": "alert_condition_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "threshold": {"name": "threshold", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "alert_threshold_type", "schema": "public", "indexes": [{"keyName": "alert_threshold_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}}, "name": "calc_data_types", "schema": "public", "indexes": [{"keyName": "calc_data_types_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(20)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 20, "mappedType": "string"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "calculation_period", "schema": "public", "indexes": [{"keyName": "calculation_period_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "dashboard_id": {"name": "dashboard_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "mappedType": "string"}, "progress": {"name": "progress", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "result": {"name": "result", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "dashboard_sync_job", "schema": "public", "indexes": [{"keyName": "dashboard_sync_job_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "sort_order": {"name": "sort_order", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "periods", "schema": "public", "indexes": [{"keyName": "periods_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "is_base": {"name": "is_base", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}}, "name": "units_group", "schema": "public", "indexes": [{"columnNames": ["is_base"], "composite": false, "keyName": "units_group_base_id", "primary": false, "unique": true}, {"keyName": "units_group_base_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "create unique index \"units_group_base_id\" on \"units_group\"(\"is_base\") where \"is_base\" is true"}, {"keyName": "units_group_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 100, "mappedType": "string"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(300)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 300, "mappedType": "string"}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "global_role": {"name": "global_role", "type": "smallint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "enum"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "enabled": {"name": "enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "default": "true", "mappedType": "boolean"}, "country_code": {"name": "country_code", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "phone_no": {"name": "phone_no", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "user", "schema": "public", "indexes": [{"columnNames": ["username"], "composite": false, "keyName": "user_username_unique", "primary": false, "unique": true}, {"columnNames": ["email"], "composite": false, "keyName": "user_email_unique", "primary": false, "unique": true}, {"keyName": "user_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_created_by_foreign": {"constraintName": "user_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.user", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "user_updated_by_foreign": {"constraintName": "user_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.user", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "measurement_type", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "measurement_type_name_key", "primary": false, "unique": true}, {"keyName": "measurement_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"measurement_type_created_by_foreign": {"constraintName": "measurement_type_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.measurement_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_type_updated_by_foreign": {"constraintName": "measurement_type_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.measurement_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "m_type": {"name": "m_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "unit_of_measure", "schema": "public", "indexes": [{"keyName": "unit_of_measure_name_m_type_key", "columnNames": ["name", "m_type"], "composite": true, "primary": false, "unique": true}, {"keyName": "unit_of_measure_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"unit_of_measure_created_by_foreign": {"constraintName": "unit_of_measure_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.unit_of_measure", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "unit_of_measure_updated_by_foreign": {"constraintName": "unit_of_measure_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.unit_of_measure", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "unit_of_measure_m_type_foreign": {"constraintName": "unit_of_measure_m_type_foreign", "columnNames": ["m_type"], "localTableName": "public.unit_of_measure", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement_type", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "unit_of_measure": {"name": "unit_of_measure", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "units_group": {"name": "units_group", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "is_default": {"name": "is_default", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "units_group_unit", "schema": "public", "indexes": [{"keyName": "units_group_unit_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"units_group_unit_unit_of_measure_foreign": {"constraintName": "units_group_unit_unit_of_measure_foreign", "columnNames": ["unit_of_measure"], "localTableName": "public.units_group_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.unit_of_measure", "updateRule": "cascade"}, "units_group_unit_units_group_foreign": {"constraintName": "units_group_unit_units_group_foreign", "columnNames": ["units_group"], "localTableName": "public.units_group_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.units_group", "deleteRule": "cascade", "updateRule": "cascade"}, "units_group_unit_created_by_foreign": {"constraintName": "units_group_unit_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.units_group_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "units_group_unit_updated_by_foreign": {"constraintName": "units_group_unit_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.units_group_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"units_group_id": {"name": "units_group_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "unit_group_unit_id": {"name": "unit_group_unit_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "units_group_default_unit", "schema": "public", "indexes": [{"keyName": "units_group_default_unit_pkey", "columnNames": ["units_group_id", "unit_group_unit_id"], "composite": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"units_group_default_unit_units_group_id_foreign": {"constraintName": "units_group_default_unit_units_group_id_foreign", "columnNames": ["units_group_id"], "localTableName": "public.units_group_default_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.units_group", "deleteRule": "cascade", "updateRule": "cascade"}, "units_group_default_unit_unit_group_unit_id_foreign": {"constraintName": "units_group_default_unit_unit_group_unit_id_foreign", "columnNames": ["unit_group_unit_id"], "localTableName": "public.units_group_default_unit", "referencedColumnNames": ["id"], "referencedTableName": "public.units_group_unit", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "location", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "location_name_key", "primary": false, "unique": true}, {"keyName": "location_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"location_created_by_foreign": {"constraintName": "location_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.location", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "location_updated_by_foreign": {"constraintName": "location_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.location", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "user": {"name": "user", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "expire": {"name": "expire", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}}, "name": "forgot_password", "schema": "public", "indexes": [{"keyName": "forgot_password_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"forgot_password_user_foreign": {"constraintName": "forgot_password_user_foreign", "columnNames": ["user"], "localTableName": "public.forgot_password", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "factor_type", "schema": "public", "indexes": [{"keyName": "factor_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"factor_type_created_by_foreign": {"constraintName": "factor_type_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.factor_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "factor_type_updated_by_foreign": {"constraintName": "factor_type_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.factor_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "data": {"name": "data", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 65536, "mappedType": "text"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "diagram", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "diagram_unique", "primary": false, "unique": true}, {"keyName": "diagram_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"diagram_created_by_foreign": {"constraintName": "diagram_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.diagram", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "diagram_updated_by_foreign": {"constraintName": "diagram_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.diagram", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "data_type", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "data_type_name_key", "primary": false, "unique": true}, {"keyName": "data_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"data_type_created_by_foreign": {"constraintName": "data_type_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.data_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "data_type_updated_by_foreign": {"constraintName": "data_type_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.data_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "expression": {"name": "expression", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 150, "mappedType": "string"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 150, "mappedType": "string"}, "imports_list": {"name": "imports_list", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 150, "mappedType": "string"}, "data_type": {"name": "data_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "calculation_template", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "calculation_name_unique", "primary": false, "unique": true}, {"keyName": "calculation_template_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_template_data_type_foreign": {"constraintName": "calculation_template_data_type_foreign", "columnNames": ["data_type"], "localTableName": "public.calculation_template", "referencedColumnNames": ["id"], "referencedTableName": "public.data_type", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 100, "mappedType": "string"}, "uri": {"name": "uri", "type": "<PERSON><PERSON><PERSON>(256)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 256, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "datasource", "schema": "public", "indexes": [{"keyName": "datasource_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"datasource_created_by_foreign": {"constraintName": "datasource_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.datasource", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "datasource_updated_by_foreign": {"constraintName": "datasource_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.datasource", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name_id": {"name": "name_id", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 150, "mappedType": "string"}, "enabled": {"name": "enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "logo": {"name": "logo", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}}, "name": "customer", "schema": "public", "indexes": [{"columnNames": ["name_id"], "composite": false, "keyName": "customer_name_id_unique", "primary": false, "unique": true}, {"keyName": "customer_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"customer_created_by_foreign": {"constraintName": "customer_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.customer", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "customer_updated_by_foreign": {"constraintName": "customer_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.customer", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "customer": {"name": "customer", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "logo": {"name": "logo", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "custom_image", "schema": "public", "indexes": [{"keyName": "custom_image_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"custom_image_customer_foreign": {"constraintName": "custom_image_customer_foreign", "columnNames": ["customer"], "localTableName": "public.custom_image", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "updateRule": "cascade"}, "custom_image_created_by_foreign": {"constraintName": "custom_image_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.custom_image", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "custom_image_updated_by_foreign": {"constraintName": "custom_image_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.custom_image", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "user_id": {"name": "user_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "role": {"name": "role", "type": "smallint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "enum"}}, "name": "customer_user_role", "schema": "public", "indexes": [{"keyName": "customer_user_role_customer_id_user_id_unique", "columnNames": ["customer_id", "user_id"], "composite": true, "primary": false, "unique": true}, {"keyName": "customer_user_role_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"customer_user_role_customer_id_foreign": {"constraintName": "customer_user_role_customer_id_foreign", "columnNames": ["customer_id"], "localTableName": "public.customer_user_role", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "updateRule": "cascade"}, "customer_user_role_user_id_foreign": {"constraintName": "customer_user_role_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.customer_user_role", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "parent_type": {"name": "parent_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "lower_case_name": {"name": "lower_case_name", "type": "VARCHAR GENERATED ALWAYS AS (LOWER(\"name\")) STORED", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "ignoreSchemaChanges": ["type", "extra"], "mappedType": "string"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "asset_type", "schema": "public", "indexes": [{"keyName": "asset_type_parent_type_lower_case_name_unique", "columnNames": ["parent_type", "lower_case_name"], "composite": true, "primary": false, "unique": true}, {"keyName": "asset_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_type_parent_type_foreign": {"constraintName": "asset_type_parent_type_foreign", "columnNames": ["parent_type"], "localTableName": "public.asset_type", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_type", "deleteRule": "set null", "updateRule": "cascade"}, "asset_type_created_by_foreign": {"constraintName": "asset_type_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_type_updated_by_foreign": {"constraintName": "asset_type_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.asset_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 150, "mappedType": "string"}, "asset_type": {"name": "asset_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "metric", "schema": "public", "indexes": [{"keyName": "metric_name_asset_type_unique", "columnNames": ["name", "asset_type"], "composite": true, "primary": false, "unique": true}, {"keyName": "metric_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"metric_asset_type_foreign": {"constraintName": "metric_asset_type_foreign", "columnNames": ["asset_type"], "localTableName": "public.metric", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_type", "updateRule": "cascade"}, "metric_created_by_foreign": {"constraintName": "metric_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.metric", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "metric_updated_by_foreign": {"constraintName": "metric_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.metric", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "manufacturer": {"name": "manufacturer", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "model_no": {"name": "model_no", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "asset_type": {"name": "asset_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "customer": {"name": "customer", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "asset_template", "schema": "public", "indexes": [{"keyName": "asset_template_manufacturer_model_no_unique", "columnNames": ["manufacturer", "model_no"], "composite": true, "primary": false, "unique": true}, {"keyName": "asset_template_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_template_asset_type_foreign": {"constraintName": "asset_template_asset_type_foreign", "columnNames": ["asset_type"], "localTableName": "public.asset_template", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_type", "updateRule": "cascade"}, "asset_template_customer_foreign": {"constraintName": "asset_template_customer_foreign", "columnNames": ["customer"], "localTableName": "public.asset_template", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "deleteRule": "set null", "updateRule": "cascade"}, "asset_template_created_by_foreign": {"constraintName": "asset_template_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset_template", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "asset_template": {"name": "asset_template", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "data": {"name": "data", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "customer": {"name": "customer", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "createdat": {"name": "createdat", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 0, "mappedType": "datetime"}, "updatedat": {"name": "updatedat", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 0, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 0, "mappedType": "datetime"}}, "name": "dashboard_template", "schema": "public", "indexes": [{"keyName": "dashboard_template_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"dashboard_template_asset_template_foreign": {"constraintName": "dashboard_template_asset_template_foreign", "columnNames": ["asset_template"], "localTableName": "public.dashboard_template", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_template", "updateRule": "cascade"}, "dashboard_template_customer_foreign": {"constraintName": "dashboard_template_customer_foreign", "columnNames": ["customer"], "localTableName": "public.dashboard_template", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_template_createdby_foreign": {"constraintName": "dashboard_template_createdby_foreign", "columnNames": ["<PERSON><PERSON>"], "localTableName": "public.dashboard_template", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}, "dashboard_template_updatedby_foreign": {"constraintName": "dashboard_template_updatedby_foreign", "columnNames": ["updatedby"], "localTableName": "public.dashboard_template", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_template_deleted_by_foreign": {"constraintName": "dashboard_template_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.dashboard_template", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "assetTemplate": {"name": "assetTemplate", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "calculation": {"name": "calculation", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "output_metric": {"name": "output_metric", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "ispersisted": {"name": "ispersisted", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "writeback": {"name": "writeback", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "poll_period": {"name": "poll_period", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "calculation_metric_instance", "schema": "public", "indexes": [{"keyName": "calculation_metric_instance_assetTemplate_output_metric_unique", "columnNames": ["assetTemplate", "output_metric"], "composite": true, "primary": false, "unique": true}, {"keyName": "calculation_metric_instance_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_metric_instance_assetTemplate_foreign": {"constraintName": "calculation_metric_instance_assetTemplate_foreign", "columnNames": ["assetTemplate"], "localTableName": "public.calculation_metric_instance", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_template", "updateRule": "cascade"}, "calculation_metric_instance_calculation_foreign": {"constraintName": "calculation_metric_instance_calculation_foreign", "columnNames": ["calculation"], "localTableName": "public.calculation_metric_instance", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_template", "updateRule": "cascade"}, "calculation_metric_instance_output_metric_foreign": {"constraintName": "calculation_metric_instance_output_metric_foreign", "columnNames": ["output_metric"], "localTableName": "public.calculation_metric_instance", "referencedColumnNames": ["id"], "referencedTableName": "public.metric", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "input_label": {"name": "input_label", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "calculation_metric_instance": {"name": "calculation_metric_instance", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "metric": {"name": "metric", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "constant_number": {"name": "constant_number", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "constant_string": {"name": "constant_string", "type": "<PERSON><PERSON><PERSON>(30)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 30, "mappedType": "string"}, "comment": {"name": "comment", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 50, "mappedType": "string"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "calculation_metric_input", "schema": "public", "indexes": [{"keyName": "calculation_metric_input_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_metric_input_calculation_metric_instance_foreign": {"constraintName": "calculation_metric_input_calculation_metric_instance_foreign", "columnNames": ["calculation_metric_instance"], "localTableName": "public.calculation_metric_input", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_metric_instance", "updateRule": "cascade"}, "calculation_metric_input_metric_foreign": {"constraintName": "calculation_metric_input_metric_foreign", "columnNames": ["metric"], "localTableName": "public.calculation_metric_input", "referencedColumnNames": ["id"], "referencedTableName": "public.metric", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "calculation_metric_instance": {"name": "calculation_metric_instance", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "calculation_metric", "schema": "public", "indexes": [{"keyName": "calculation_metric_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_metric_calculation_metric_instance_foreign": {"constraintName": "calculation_metric_calculation_metric_instance_foreign", "columnNames": ["calculation_metric_instance"], "localTableName": "public.calculation_metric", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_metric_instance", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "enabled": {"name": "enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "latitude": {"name": "latitude", "type": "float4", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "float"}, "longitude": {"name": "longitude", "type": "float4", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "float"}, "a_type": {"name": "a_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "assetTemplate": {"name": "assetTemplate", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "UnitsGroup": {"name": "UnitsGroup", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "customer": {"name": "customer", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 100, "mappedType": "string"}, "timezone": {"name": "timezone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "asset", "schema": "public", "indexes": [{"columnNames": ["a_type"], "composite": false, "keyName": "asset_a_type_idx", "primary": false, "unique": false}, {"keyName": "asset_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_a_type_foreign": {"constraintName": "asset_a_type_foreign", "columnNames": ["a_type"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_type", "updateRule": "cascade"}, "asset_assetTemplate_foreign": {"constraintName": "asset_assetTemplate_foreign", "columnNames": ["assetTemplate"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_template", "deleteRule": "set null", "updateRule": "cascade"}, "asset_UnitsGroup_foreign": {"constraintName": "asset_UnitsGroup_foreign", "columnNames": ["UnitsGroup"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.units_group", "deleteRule": "set null", "updateRule": "cascade"}, "asset_customer_foreign": {"constraintName": "asset_customer_foreign", "columnNames": ["customer"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "updateRule": "cascade"}, "asset_created_by_foreign": {"constraintName": "asset_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_updated_by_foreign": {"constraintName": "asset_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_deleted_by_foreign": {"constraintName": "asset_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.asset", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "data": {"name": "data", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "asset": {"name": "asset", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "dashboardTemplate": {"name": "dashboardTemplate", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "dashboard", "schema": "public", "indexes": [{"keyName": "dashboard_title_customer_id_unique", "columnNames": ["title", "customer_id"], "composite": true, "primary": false, "unique": true}, {"keyName": "dashboard_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"dashboard_created_by_foreign": {"constraintName": "dashboard_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_updated_by_foreign": {"constraintName": "dashboard_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_customer_id_foreign": {"constraintName": "dashboard_customer_id_foreign", "columnNames": ["customer_id"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "updateRule": "cascade"}, "dashboard_asset_foreign": {"constraintName": "dashboard_asset_foreign", "columnNames": ["asset"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_dashboardTemplate_foreign": {"constraintName": "dashboard_dashboardTemplate_foreign", "columnNames": ["dashboardTemplate"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.dashboard_template", "deleteRule": "set null", "updateRule": "cascade"}, "dashboard_deleted_by_foreign": {"constraintName": "dashboard_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.dashboard", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"dashboard_id": {"name": "dashboard_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "user_id": {"name": "user_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "date", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "date"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "dashboard_favorite", "schema": "public", "indexes": [{"keyName": "dashboard_favorite_pkey", "columnNames": ["dashboard_id", "customer_id", "user_id"], "composite": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"dashboard_favorite_dashboard_id_foreign": {"constraintName": "dashboard_favorite_dashboard_id_foreign", "columnNames": ["dashboard_id"], "localTableName": "public.dashboard_favorite", "referencedColumnNames": ["id"], "referencedTableName": "public.dashboard", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_favorite_customer_id_foreign": {"constraintName": "dashboard_favorite_customer_id_foreign", "columnNames": ["customer_id"], "localTableName": "public.dashboard_favorite", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_favorite_user_id_foreign": {"constraintName": "dashboard_favorite_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.dashboard_favorite", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_favorite_created_by_foreign": {"constraintName": "dashboard_favorite_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.dashboard_favorite", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "user_id": {"name": "user_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "dashboard_id": {"name": "dashboard_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "date", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "date"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "dashboard_default", "schema": "public", "indexes": [{"keyName": "dashboard_default_pkey", "columnNames": ["customer_id", "user_id"], "composite": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"dashboard_default_customer_id_foreign": {"constraintName": "dashboard_default_customer_id_foreign", "columnNames": ["customer_id"], "localTableName": "public.dashboard_default", "referencedColumnNames": ["id"], "referencedTableName": "public.customer", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_default_user_id_foreign": {"constraintName": "dashboard_default_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.dashboard_default", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_default_dashboard_id_foreign": {"constraintName": "dashboard_default_dashboard_id_foreign", "columnNames": ["dashboard_id"], "localTableName": "public.dashboard_default", "referencedColumnNames": ["id"], "referencedTableName": "public.dashboard", "deleteRule": "cascade", "updateRule": "cascade"}, "dashboard_default_created_by_foreign": {"constraintName": "dashboard_default_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.dashboard_default", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "child": {"name": "child", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "parent": {"name": "parent", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "is_default": {"name": "is_default", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "asset_hier", "schema": "public", "indexes": [{"keyName": "pa_chi_unique", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "create unique index \"pa_chi_unique\" on \"asset_hier\"(\"child\", \"parent\") where \"deleted_at\" is null"}, {"keyName": "asset_hier_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_hier_child_foreign": {"constraintName": "asset_hier_child_foreign", "columnNames": ["child"], "localTableName": "public.asset_hier", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "deleteRule": "cascade", "updateRule": "cascade"}, "asset_hier_parent_foreign": {"constraintName": "asset_hier_parent_foreign", "columnNames": ["parent"], "localTableName": "public.asset_hier", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "deleteRule": "set null", "updateRule": "cascade"}, "asset_hier_created_by_foreign": {"constraintName": "asset_hier_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset_hier", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_hier_updated_by_foreign": {"constraintName": "asset_hier_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.asset_hier", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_hier_deleted_by_foreign": {"constraintName": "asset_hier_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.asset_hier", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "user_id": {"name": "user_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "prefer_key": {"name": "prefer_key", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["DATE_FORMAT", "CURRENCY", "DEFAULT_CUSTOMER"], "mappedType": "enum"}, "prefer_value": {"name": "prefer_value", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}}, "name": "user_preferences", "schema": "public", "indexes": [{"keyName": "user_preferences_unique", "columnNames": ["prefer_key", "user_id"], "composite": true, "primary": false, "unique": true}, {"keyName": "user_preferences_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_preferences_user_id_foreign": {"constraintName": "user_preferences_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 50, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "value_type", "schema": "public", "indexes": [{"columnNames": ["name"], "composite": false, "keyName": "value_type_name_key", "primary": false, "unique": true}, {"keyName": "value_type_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"value_type_created_by_foreign": {"constraintName": "value_type_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.value_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "value_type_updated_by_foreign": {"constraintName": "value_type_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.value_type", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 150, "mappedType": "string"}, "enabled": {"name": "enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "metric": {"name": "metric", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "m_type": {"name": "m_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "unit_of_measure": {"name": "unit_of_measure", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "data_type": {"name": "data_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 100, "mappedType": "string"}, "value_type": {"name": "value_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "datasource": {"name": "datasource", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "meter_factor": {"name": "meter_factor", "type": "double precision", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "double"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "writeback": {"name": "writeback", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "measurement", "schema": "public", "indexes": [{"columnNames": ["m_type"], "composite": false, "keyName": "measurement_m_type_idx", "primary": false, "unique": false}, {"columnNames": ["unit_of_measure"], "composite": false, "keyName": "measurement_uom_idx", "primary": false, "unique": false}, {"columnNames": ["data_type"], "composite": false, "keyName": "measurement_data_type_idx", "primary": false, "unique": false}, {"columnNames": ["value_type"], "composite": false, "keyName": "measurement_value_type_idx", "primary": false, "unique": false}, {"keyName": "measurement_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"measurement_metric_foreign": {"constraintName": "measurement_metric_foreign", "columnNames": ["metric"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.metric", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_m_type_foreign": {"constraintName": "measurement_m_type_foreign", "columnNames": ["m_type"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement_type", "updateRule": "cascade"}, "measurement_unit_of_measure_foreign": {"constraintName": "measurement_unit_of_measure_foreign", "columnNames": ["unit_of_measure"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.unit_of_measure", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_data_type_foreign": {"constraintName": "measurement_data_type_foreign", "columnNames": ["data_type"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.data_type", "updateRule": "cascade"}, "measurement_value_type_foreign": {"constraintName": "measurement_value_type_foreign", "columnNames": ["value_type"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.value_type", "updateRule": "cascade"}, "measurement_datasource_foreign": {"constraintName": "measurement_datasource_foreign", "columnNames": ["datasource"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.datasource", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_created_by_foreign": {"constraintName": "measurement_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_updated_by_foreign": {"constraintName": "measurement_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "measurement_deleted_by_foreign": {"constraintName": "measurement_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "measurement": {"name": "measurement", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "factor_type": {"name": "factor_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "seasonal": {"name": "seasonal", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}}, "name": "time_varying_factor", "schema": "public", "indexes": [{"columnNames": ["measurement"], "composite": false, "keyName": "tv_factors_meas_unique", "primary": false, "unique": true}, {"columnNames": ["factor_type"], "composite": false, "keyName": "time_var_factor_factor_type_idx", "primary": false, "unique": false}, {"keyName": "time_varying_factor_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"time_varying_factor_measurement_foreign": {"constraintName": "time_varying_factor_measurement_foreign", "columnNames": ["measurement"], "localTableName": "public.time_varying_factor", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "deleteRule": "set null", "updateRule": "cascade"}, "time_varying_factor_factor_type_foreign": {"constraintName": "time_varying_factor_factor_type_foreign", "columnNames": ["factor_type"], "localTableName": "public.time_varying_factor", "referencedColumnNames": ["id"], "referencedTableName": "public.factor_type", "deleteRule": "set null", "updateRule": "cascade"}, "time_varying_factor_created_by_foreign": {"constraintName": "time_varying_factor_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.time_varying_factor", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "time_varying_factor_updated_by_foreign": {"constraintName": "time_varying_factor_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.time_varying_factor", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "factor": {"name": "factor", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "effective_date": {"name": "effective_date", "type": "date", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "now()", "mappedType": "date"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "factor_schedule", "schema": "public", "indexes": [{"keyName": "factor_schedule_unique", "columnNames": ["factor", "effective_date"], "composite": true, "primary": false, "unique": true}, {"keyName": "factor_schedule_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"factor_schedule_factor_foreign": {"constraintName": "factor_schedule_factor_foreign", "columnNames": ["factor"], "localTableName": "public.factor_schedule", "referencedColumnNames": ["id"], "referencedTableName": "public.time_varying_factor", "deleteRule": "set null", "updateRule": "cascade"}, "factor_schedule_created_by_foreign": {"constraintName": "factor_schedule_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.factor_schedule", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "factor_schedule_updated_by_foreign": {"constraintName": "factor_schedule_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.factor_schedule", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "factor_schedule": {"name": "factor_schedule", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "time_of_day": {"name": "time_of_day", "type": "time(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'00:00:00'", "mappedType": "time"}, "weekday": {"name": "weekday", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "value": {"name": "value", "type": "double precision", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0.0", "mappedType": "double"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "factor_time_of_day_value", "schema": "public", "indexes": [{"keyName": "factor_time_of_day_value_unique", "columnNames": ["factor_schedule", "time_of_day", "weekday"], "composite": true, "primary": false, "unique": true}, {"keyName": "factor_time_of_day_value_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"factor_time_of_day_value_factor_schedule_foreign": {"constraintName": "factor_time_of_day_value_factor_schedule_foreign", "columnNames": ["factor_schedule"], "localTableName": "public.factor_time_of_day_value", "referencedColumnNames": ["id"], "referencedTableName": "public.factor_schedule", "deleteRule": "set null", "updateRule": "cascade"}, "factor_time_of_day_value_created_by_foreign": {"constraintName": "factor_time_of_day_value_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.factor_time_of_day_value", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "factor_time_of_day_value_updated_by_foreign": {"constraintName": "factor_time_of_day_value_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.factor_time_of_day_value", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "factor_time_of_day_value_deleted_by_foreign": {"constraintName": "factor_time_of_day_value_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.factor_time_of_day_value", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "calculation": {"name": "calculation", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "output_measurement": {"name": "output_measurement", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "ispersisted": {"name": "ispersisted", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "poll_period": {"name": "poll_period", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "calculation_instance", "schema": "public", "indexes": [{"columnNames": ["output_measurement"], "composite": false, "keyName": "calculation_instance_output_measurement_unique", "primary": false, "unique": true}, {"keyName": "calculation_instance_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_instance_calculation_foreign": {"constraintName": "calculation_instance_calculation_foreign", "columnNames": ["calculation"], "localTableName": "public.calculation_instance", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_template", "updateRule": "cascade"}, "calculation_instance_output_measurement_foreign": {"constraintName": "calculation_instance_output_measurement_foreign", "columnNames": ["output_measurement"], "localTableName": "public.calculation_instance", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "created": {"name": "created", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated": {"name": "updated", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "createdby": {"name": "<PERSON><PERSON>", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updatedby": {"name": "updatedby", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "input_label": {"name": "input_label", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "calculation_instance": {"name": "calculation_instance", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "measurement": {"name": "measurement", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "constant_number": {"name": "constant_number", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}, "constant_string": {"name": "constant_string", "type": "<PERSON><PERSON><PERSON>(30)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 30, "mappedType": "string"}, "comment": {"name": "comment", "type": "<PERSON><PERSON><PERSON>(50)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 50, "mappedType": "string"}}, "name": "calculation_input", "schema": "public", "indexes": [{"keyName": "calc_instance_input_unique", "columnNames": ["calculation_instance", "input_label"], "composite": true, "primary": false, "unique": true}, {"keyName": "calculation_input_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"calculation_input_calculation_instance_foreign": {"constraintName": "calculation_input_calculation_instance_foreign", "columnNames": ["calculation_instance"], "localTableName": "public.calculation_input", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_instance", "updateRule": "cascade"}, "calculation_input_measurement_foreign": {"constraintName": "calculation_input_measurement_foreign", "columnNames": ["measurement"], "localTableName": "public.calculation_input", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "measurement": {"name": "measurement", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "asset": {"name": "asset", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "location": {"name": "location", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "asset_measurement", "schema": "public", "indexes": [{"keyName": "asset_measurement_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_measurement_measurement_foreign": {"constraintName": "asset_measurement_measurement_foreign", "columnNames": ["measurement"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "deleteRule": "cascade", "updateRule": "cascade"}, "asset_measurement_asset_foreign": {"constraintName": "asset_measurement_asset_foreign", "columnNames": ["asset"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "deleteRule": "cascade", "updateRule": "cascade"}, "asset_measurement_location_foreign": {"constraintName": "asset_measurement_location_foreign", "columnNames": ["location"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.location", "deleteRule": "set null", "updateRule": "cascade"}, "asset_measurement_created_by_foreign": {"constraintName": "asset_measurement_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_measurement_updated_by_foreign": {"constraintName": "asset_measurement_updated_by_foreign", "columnNames": ["updated_by"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}, "asset_measurement_deleted_by_foreign": {"constraintName": "asset_measurement_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.asset_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "dashboard": {"name": "dashboard", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "measurement_id": {"name": "measurement_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "widget_id": {"name": "widget_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "time_of_annotation": {"name": "time_of_annotation", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "value": {"name": "value", "type": "double precision", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "double"}, "settings": {"name": "settings", "type": "<PERSON><PERSON><PERSON>(65536)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 65536, "mappedType": "string"}}, "name": "annotation", "schema": "public", "indexes": [{"keyName": "annotation_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"annotation_dashboard_foreign": {"constraintName": "annotation_dashboard_foreign", "columnNames": ["dashboard"], "localTableName": "public.annotation", "referencedColumnNames": ["id"], "referencedTableName": "public.dashboard", "updateRule": "cascade"}, "annotation_measurement_id_foreign": {"constraintName": "annotation_measurement_id_foreign", "columnNames": ["measurement_id"], "localTableName": "public.annotation", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "asset_id": {"name": "asset_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "measurement_id": {"name": "measurement_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "agg": {"name": "agg", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "period": {"name": "period", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "threshold_type": {"name": "threshold_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "condition": {"name": "condition", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "threshold_value": {"name": "threshold_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "reset_deadband": {"name": "reset_deadband", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 150, "mappedType": "string"}, "notification_type": {"name": "notification_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "customer_id": {"name": "customer_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "enabled": {"name": "enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "state": {"name": "state", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "deleted_by": {"name": "deleted_by", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "alerts", "schema": "public", "indexes": [{"keyName": "alerts_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"alerts_asset_id_foreign": {"constraintName": "alerts_asset_id_foreign", "columnNames": ["asset_id"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "updateRule": "cascade"}, "alerts_measurement_id_foreign": {"constraintName": "alerts_measurement_id_foreign", "columnNames": ["measurement_id"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "updateRule": "cascade"}, "alerts_agg_foreign": {"constraintName": "alerts_agg_foreign", "columnNames": ["agg"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.aggregates", "updateRule": "cascade"}, "alerts_period_foreign": {"constraintName": "alerts_period_foreign", "columnNames": ["period"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.periods", "deleteRule": "set null", "updateRule": "cascade"}, "alerts_threshold_type_foreign": {"constraintName": "alerts_threshold_type_foreign", "columnNames": ["threshold_type"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.alert_threshold_type", "updateRule": "cascade"}, "alerts_condition_foreign": {"constraintName": "alerts_condition_foreign", "columnNames": ["condition"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.alert_condition", "deleteRule": "set null", "updateRule": "cascade"}, "alerts_deleted_by_foreign": {"constraintName": "alerts_deleted_by_foreign", "columnNames": ["deleted_by"], "localTableName": "public.alerts", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "alert": {"name": "alert", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "user": {"name": "user", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "alert_state": {"name": "alert_state", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(20)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 20, "mappedType": "string"}, "type": {"name": "type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "reason": {"name": "reason", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "notify_failure", "schema": "public", "indexes": [{"keyName": "notify_failure_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"notify_failure_alert_foreign": {"constraintName": "notify_failure_alert_foreign", "columnNames": ["alert"], "localTableName": "public.notify_failure", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "deleteRule": "cascade", "updateRule": "cascade"}, "notify_failure_user_foreign": {"constraintName": "notify_failure_user_foreign", "columnNames": ["user"], "localTableName": "public.notify_failure", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "alert_id": {"name": "alert_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "start_time": {"name": "start_time", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 0, "mappedType": "datetime"}, "end_time": {"name": "end_time", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 0, "mappedType": "datetime"}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "max_value": {"name": "max_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "min_value": {"name": "min_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "avg_value": {"name": "avg_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "time_duration": {"name": "time_duration", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "string"}}, "name": "excursion_stats", "schema": "public", "indexes": [{"keyName": "excursion_stats_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"excursion_stats_alert_id_foreign": {"constraintName": "excursion_stats_alert_id_foreign", "columnNames": ["alert_id"], "localTableName": "public.excursion_stats", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "timestamp": {"name": "timestamp", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 0, "mappedType": "datetime"}, "deadband": {"name": "deadband", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "state": {"name": "state", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "limit": {"name": "limit", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "input_value": {"name": "input_value", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "condition": {"name": "condition", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "aggregate": {"name": "aggregate", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "period": {"name": "period", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "asset": {"name": "asset", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "measurement": {"name": "measurement", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "alert_id": {"name": "alert_id", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "events", "schema": "public", "indexes": [{"keyName": "events_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"events_condition_foreign": {"constraintName": "events_condition_foreign", "columnNames": ["condition"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.alert_condition", "updateRule": "cascade"}, "events_aggregate_foreign": {"constraintName": "events_aggregate_foreign", "columnNames": ["aggregate"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.aggregates", "updateRule": "cascade"}, "events_period_foreign": {"constraintName": "events_period_foreign", "columnNames": ["period"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.periods", "updateRule": "cascade"}, "events_asset_foreign": {"constraintName": "events_asset_foreign", "columnNames": ["asset"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.asset", "updateRule": "cascade"}, "events_measurement_foreign": {"constraintName": "events_measurement_foreign", "columnNames": ["measurement"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement", "updateRule": "cascade"}, "events_alert_id_foreign": {"constraintName": "events_alert_id_foreign", "columnNames": ["alert_id"], "localTableName": "public.events", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "updateRule": "cascade"}}}, {"columns": {"alert": {"name": "alert", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "learning_period": {"name": "learning_period", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "string"}, "include_velocity": {"name": "include_velocity", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "include_momentum": {"name": "include_momentum", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 0, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 0, "default": "now()", "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "anomaly_parameter", "schema": "public", "indexes": [{"columnNames": ["alert"], "composite": false, "keyName": "anomaly_parameter_alert_unique", "primary": false, "unique": true}, {"keyName": "anomaly_parameter_pkey", "columnNames": ["alert"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"anomaly_parameter_alert_foreign": {"constraintName": "anomaly_parameter_alert_foreign", "columnNames": ["alert"], "localTableName": "public.anomaly_parameter", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "alert": {"name": "alert", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "model": {"name": "model", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "anomaly_measure": {"name": "anomaly_measure", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "bigint"}, "created_at": {"name": "created_at", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 0, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz(0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 0, "default": "now()", "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "updated_by": {"name": "updated_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}}, "name": "anomaly_model", "schema": "public", "indexes": [{"columnNames": ["alert"], "composite": false, "keyName": "anomaly_model_alert_unique", "primary": false, "unique": true}, {"keyName": "anomaly_model_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"anomaly_model_alert_foreign": {"constraintName": "anomaly_model_alert_foreign", "columnNames": ["alert"], "localTableName": "public.anomaly_model", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "user": {"name": "user", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "alert": {"name": "alert", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "notificationtype": {"name": "notificationtype", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}}, "name": "alert_users", "schema": "public", "indexes": [{"keyName": "alert_users_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"alert_users_user_foreign": {"constraintName": "alert_users_user_foreign", "columnNames": ["user"], "localTableName": "public.alert_users", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}, "alert_users_alert_foreign": {"constraintName": "alert_users_alert_foreign", "columnNames": ["alert"], "localTableName": "public.alert_users", "referencedColumnNames": ["id"], "referencedTableName": "public.alerts", "deleteRule": "cascade", "updateRule": "cascade"}}}, {"columns": {"id": {"name": "id", "type": "serial", "unsigned": true, "autoincrement": true, "primary": true, "nullable": false, "mappedType": "integer"}, "metric": {"name": "metric", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "asset_template": {"name": "asset_template", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "m_type": {"name": "m_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "data_type": {"name": "data_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "location": {"name": "location", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "value_type": {"name": "value_type", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "datasource": {"name": "datasource", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "calculation_metric_instance": {"name": "calculation_metric_instance", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(150)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 150, "mappedType": "string"}, "meter_factor": {"name": "meter_factor", "type": "double precision", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "double"}, "created_at": {"name": "created_at", "type": "timestamptz(6)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "created_by": {"name": "created_by", "type": "int", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "writeback": {"name": "writeback", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "boolean"}}, "name": "asset_template_measurement", "schema": "public", "indexes": [{"keyName": "asset_template_measurement_asset_template_metric_unique", "columnNames": ["asset_template", "metric"], "composite": true, "primary": false, "unique": true}, {"keyName": "asset_template_measurement_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"asset_template_measurement_metric_foreign": {"constraintName": "asset_template_measurement_metric_foreign", "columnNames": ["metric"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.metric", "deleteRule": "set null", "updateRule": "cascade"}, "asset_template_measurement_asset_template_foreign": {"constraintName": "asset_template_measurement_asset_template_foreign", "columnNames": ["asset_template"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.asset_template", "deleteRule": "cascade", "updateRule": "cascade"}, "asset_template_measurement_m_type_foreign": {"constraintName": "asset_template_measurement_m_type_foreign", "columnNames": ["m_type"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.measurement_type", "updateRule": "cascade"}, "asset_template_measurement_data_type_foreign": {"constraintName": "asset_template_measurement_data_type_foreign", "columnNames": ["data_type"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.data_type", "updateRule": "cascade"}, "asset_template_measurement_location_foreign": {"constraintName": "asset_template_measurement_location_foreign", "columnNames": ["location"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.location", "deleteRule": "set null", "updateRule": "cascade"}, "asset_template_measurement_value_type_foreign": {"constraintName": "asset_template_measurement_value_type_foreign", "columnNames": ["value_type"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.value_type", "updateRule": "cascade"}, "asset_template_measurement_datasource_foreign": {"constraintName": "asset_template_measurement_datasource_foreign", "columnNames": ["datasource"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.datasource", "deleteRule": "set null", "updateRule": "cascade"}, "asset_template_measurement_calculation_metric_instance_foreign": {"constraintName": "asset_template_measurement_calculation_metric_instance_foreign", "columnNames": ["calculation_metric_instance"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.calculation_metric_instance", "deleteRule": "set null", "updateRule": "cascade"}, "asset_template_measurement_created_by_foreign": {"constraintName": "asset_template_measurement_created_by_foreign", "columnNames": ["created_by"], "localTableName": "public.asset_template_measurement", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "deleteRule": "set null", "updateRule": "cascade"}}}]}