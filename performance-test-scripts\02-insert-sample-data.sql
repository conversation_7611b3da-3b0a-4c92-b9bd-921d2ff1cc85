-- Insert sample data for performance testing
-- This creates a realistic dataset to test getRangeGroupedStats performance

-- Insert lookup data
INSERT INTO "user" ("id", "email", "name") VALUES 
(1, '<EMAIL>', 'Admin User'),
(2, '<EMAIL>', 'Test User');

INSERT INTO "customer" ("id", "name") VALUES 
(1, 'Test Customer 1'),
(2, 'Test Customer 2');

INSERT INTO "asset_type" ("id", "name") VALUES 
(1, 'Pump'),
(2, 'Motor'),
(3, 'Sensor'),
(4, 'Valve'),
(5, 'Tank');

INSERT INTO "measurement_type" ("id", "name") VALUES 
(1, 'Temperature'),
(2, 'Pressure'),
(3, 'Flow Rate'),
(4, 'Vibration'),
(5, 'Level');

INSERT INTO "data_type" ("id", "name") VALUES 
(1, 'Analog'),
(2, 'Digital'),
(3, 'Calculated');

INSERT INTO "alert_threshold_type" ("id", "name") VALUES 
(1, 'High Alarm'),
(2, 'Low Alarm'),
(3, 'High Warning'),
(4, 'Low Warning'),
(5, 'Deviation');

INSERT INTO "aggregates" ("id", "name") VALUES 
(1, 'Average'),
(2, 'Maximum'),
(3, 'Minimum'),
(4, 'Sum');

INSERT INTO "periods" ("id", "name") VALUES 
(1, '1 Minute'),
(2, '5 Minutes'),
(3, '15 Minutes'),
(4, '1 Hour');

-- Generate assets (100 assets per customer)
INSERT INTO "asset" ("id", "tag", "enabled", "a_type", "customer", "description", "created")
SELECT 
    generate_series(1, 200) as id,
    'ASSET_' || LPAD(generate_series(1, 200)::text, 4, '0') as tag,
    true as enabled,
    ((generate_series(1, 200) - 1) % 5) + 1 as a_type,
    CASE WHEN generate_series(1, 200) <= 100 THEN 1 ELSE 2 END as customer,
    'Test Asset ' || generate_series(1, 200) as description,
    NOW() - (random() * interval '365 days') as created;

-- Generate measurements (5 measurements per asset)
INSERT INTO "measurement" ("id", "tag", "enabled", "m_type", "data_type", "description", "created")
SELECT 
    generate_series(1, 1000) as id,
    'MEAS_' || LPAD(generate_series(1, 1000)::text, 5, '0') as tag,
    true as enabled,
    ((generate_series(1, 1000) - 1) % 5) + 1 as m_type,
    ((generate_series(1, 1000) - 1) % 3) + 1 as data_type,
    'Test Measurement ' || generate_series(1, 1000) as description,
    NOW() - (random() * interval '365 days') as created;

-- Generate alerts (2 alerts per asset, so 400 total)
INSERT INTO "alerts" ("id", "asset_id", "measurement_id", "agg", "period", "threshold_type", "threshold_value", "customer_id", "enabled", "description", "created_at")
SELECT 
    generate_series(1, 400) as id,
    ((generate_series(1, 400) - 1) / 2) + 1 as asset_id,
    ((generate_series(1, 400) - 1) % 1000) + 1 as measurement_id,
    ((generate_series(1, 400) - 1) % 4) + 1 as agg,
    ((generate_series(1, 400) - 1) % 4) + 1 as period,
    ((generate_series(1, 400) - 1) % 5) + 1 as threshold_type,
    50.0 + (random() * 100.0) as threshold_value,
    CASE WHEN ((generate_series(1, 400) - 1) / 2) + 1 <= 100 THEN 1 ELSE 2 END as customer_id,
    true as enabled,
    'Test Alert ' || generate_series(1, 400) as description,
    NOW() - (random() * interval '365 days') as created_at;

-- Generate excursion_stats (this is the critical table for performance testing)
-- Create 50,000 excursion records over the last 90 days to simulate production load
INSERT INTO "excursion_stats" ("id", "alert_id", "start_time", "end_time", "duration", "max_value", "min_value", "avg_value", "time_duration")
SELECT 
    generate_series(1, 50000) as id,
    ((generate_series(1, 50000) - 1) % 400) + 1 as alert_id,
    NOW() - (random() * interval '90 days') as start_time,
    NOW() - (random() * interval '90 days') + (random() * interval '2 hours') as end_time,
    LPAD((random() * 120)::int::text, 2, '0') || ':' || LPAD((random() * 60)::int::text, 2, '0') || ':' || LPAD((random() * 60)::int::text, 2, '0') as duration,
    50.0 + (random() * 100.0) as max_value,
    10.0 + (random() * 40.0) as min_value,
    30.0 + (random() * 60.0) as avg_value,
    make_interval(
        hours => (random() * 2)::int,
        mins => (random() * 60)::int,
        secs => (random() * 60)::int
    ) as time_duration;

-- Update sequences to correct values
SELECT setval('user_id_seq', (SELECT MAX(id) FROM "user"));
SELECT setval('customer_id_seq', (SELECT MAX(id) FROM "customer"));
SELECT setval('asset_type_id_seq', (SELECT MAX(id) FROM "asset_type"));
SELECT setval('measurement_type_id_seq', (SELECT MAX(id) FROM "measurement_type"));
SELECT setval('data_type_id_seq', (SELECT MAX(id) FROM "data_type"));
SELECT setval('alert_threshold_type_id_seq', (SELECT MAX(id) FROM "alert_threshold_type"));
SELECT setval('aggregates_id_seq', (SELECT MAX(id) FROM "aggregates"));
SELECT setval('periods_id_seq', (SELECT MAX(id) FROM "periods"));
SELECT setval('asset_id_seq', (SELECT MAX(id) FROM "asset"));
SELECT setval('measurement_id_seq', (SELECT MAX(id) FROM "measurement"));
SELECT setval('alerts_id_seq', (SELECT MAX(id) FROM "alerts"));
SELECT setval('excursion_stats_id_seq', (SELECT MAX(id) FROM "excursion_stats"));

-- Create some statistics for the query planner
ANALYZE "asset";
ANALYZE "measurement";
ANALYZE "alerts";
ANALYZE "excursion_stats";

-- Display data summary
SELECT 'Data Summary' as info;
SELECT 'Assets: ' || COUNT(*) as count FROM "asset";
SELECT 'Measurements: ' || COUNT(*) as count FROM "measurement";
SELECT 'Alerts: ' || COUNT(*) as count FROM "alerts";
SELECT 'Excursion Stats: ' || COUNT(*) as count FROM "excursion_stats";
SELECT 'Customer 1 Excursions: ' || COUNT(*) as count 
FROM "excursion_stats" es 
JOIN "alerts" a ON es.alert_id = a.id 
WHERE a.customer_id = 1;
