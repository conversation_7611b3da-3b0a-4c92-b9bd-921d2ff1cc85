import { MikroOrmModule } from "@mikro-orm/nestjs";
import { Module } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { CustomersModule } from "src/customers/customers.module";
import { AssetTypeService } from "./asset-type.service";
import { AssetApiController } from "./asset.controller";
import { AssetService } from "./asset.service";
import { AssetHierarchy } from "./domain/asset-hierarchy.entity";
import { AssetType } from "./domain/asset-type.entity";
import { Asset } from "./domain/asset.entity";
import { AssetsBackofficeApiController } from "./assets-backoffice.controller";
import { TimeZoneService } from "./timezone.service";
import { AssetRepository } from "./repository/asset.repository";
import { AssetHierarchyService } from "./asset-hierarchy.service";
import { AssetHierarchyRepository } from "./repository/asset-hierarchy.repository";
import { DatabaseModule } from "src/db/database.module";
import { HierarchyGraphService } from "./hierarchy-graph.service";
import { Metric } from "./domain/metric.entity";
import { MetricService } from "./metric.service";
import { AssetTypeRepository } from "./repository/asset-type.repository";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Alerts } from "src/alert/domain/alert.entity";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      AssetTemplate,
      Asset,
      AssetHierarchy,
      AssetType,
      Metric,
      AssetMeasurement,
      Alerts,
      Dashboard,
    ]),
    AuthModule,
    CustomersModule,
    DatabaseModule,
  ],
  providers: [
    AssetTypeService,
    AssetTypeRepository,
    TimeZoneService,
    AssetService,
    AssetRepository,
    AssetHierarchyService,
    HierarchyGraphService,
    AssetHierarchyRepository,
    MetricService,
  ],
  controllers: [AssetsBackofficeApiController, AssetApiController],
  exports: [AssetService, AssetTypeService, MetricService, AssetRepository],
})
export class AssetModule {}
