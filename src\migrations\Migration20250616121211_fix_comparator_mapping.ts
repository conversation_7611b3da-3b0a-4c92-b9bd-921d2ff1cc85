import { Migration } from '@mikro-orm/migrations';

export class Migration20250616121211_fix_comparator_mapping extends Migration {

  async up(): Promise<void> {
    this.addSql(`
      -- Step 1: Assign temporary IDs to avoid collisions
      UPDATE alert_condition SET id = 99 WHERE condition = 'GE';
      UPDATE alert_condition SET id = 98 WHERE condition = 'LE';
      UPDATE alert_condition SET id = 97 WHERE condition = 'GT';

      -- Step 2: Assign correct IDs as per enum.py
      UPDATE alert_condition SET id = 2 WHERE condition = 'LE';
      UPDATE alert_condition SET id = 3 WHERE condition = 'GT';
      UPDATE alert_condition SET id = 4 WHERE condition = 'GE';
    `);
  }

  async down(): Promise<void> {
    this.addSql(`
      -- Revert changes using reverse temporary IDs
      UPDATE alert_condition SET id = 99 WHERE condition = 'GE';
      UPDATE alert_condition SET id = 98 WHERE condition = 'GT';
      UPDATE alert_condition SET id = 97 WHERE condition = 'LE';

      -- Restore original IDs
      UPDATE alert_condition SET id = 2 WHERE condition = 'GE';
      UPDATE alert_condition SET id = 3 WHERE condition = 'LE';
      UPDATE alert_condition SET id = 4 WHERE condition = 'GT';
    `);
  }

}
