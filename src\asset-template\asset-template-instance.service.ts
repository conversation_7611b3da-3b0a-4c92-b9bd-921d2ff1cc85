import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { Injectable } from "@nestjs/common";
import { AssetCreationData, AssetService } from "src/assets/asset.service";
import { MetricId } from "src/assets/domain/metric.entity";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { InvalidInputException } from "src/errors/exceptions";
import { AssetMeasurementService } from "src/measurements/asset-measurement.service";
import { DatasourceService } from "src/measurements/datasource.service";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { UnitOfMeasure } from "src/measurements/domain/unit-of-measure.entity";
import { UserId } from "src/users/domain/user.entity";
import { UnitsGroupId } from "../measurements/domain/units-group.entity";
import { UnitsGroupService } from "../measurements/units-group.service";
import { AssetTemplateService } from "./asset-template.service";
import {
  AssetMeasurementOverride,
  AssetTemplateInstance,
  AssetTemplateMeasurementInstance,
} from "./domain/asset-template-instance";
import { AssetTemplateId } from "./domain/asset-template.entity";
import { CalculationInstance } from "src/calc-engine/domain/calculation-instance.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { CalculationInput } from "src/calc-engine/domain/calculation-input.entity";
import { CalculationMetricInput } from "src/calc-metrics-controller/domain/calculation-metric-input.entity";
import { UnitOfMeasureService } from "src/measurements/unit-of-measure.service";

@Injectable()
export class AssetTemplateInstanceService {
  constructor(
    private readonly assetTemplateService: AssetTemplateService,
    private readonly assetService: AssetService,
    private readonly assetMeasurementService: AssetMeasurementService,
    private readonly unitsGroupService: UnitsGroupService,
    private readonly unitOfMeasureService: UnitOfMeasureService,
    private readonly transactionFactory: TransactionFactory,
    private readonly dataSourceService: DatasourceService,
    @InjectRepository(CalculationMetricInstance)
    private calcMetricInstanceRepository: EntityRepository<CalculationMetricInstance>,
    @InjectRepository(CalculationMetricInput)
    private calculationMetricInput: EntityRepository<CalculationMetricInput>,
    @InjectRepository(CalculationTemplate)
    private calcTemplateRepository: EntityRepository<CalculationTemplate>,
    @InjectRepository(CalculationInstance)
    private calcInstanceRepository: EntityRepository<CalculationInstance>,
    @InjectRepository(Measurement)
    private measurementRepository: EntityRepository<Measurement>,
    @InjectRepository(CalculationInput)
    private calculationInputRepo: EntityRepository<CalculationInput>
  ) {}

  private assertHasMetric(
    assetMeasurement: AssetMeasurement
  ): assetMeasurement is AssetTemplateMeasurementInstance {
    return assetMeasurement.metricId !== null;
  }
  async create(
    assetTemplateId: AssetTemplateId,
    asset: AssetCreationData,
    measurementOverrides: Map<MetricId, AssetMeasurementOverride>,
    unitsGroupId: UnitsGroupId,
    runById: UserId,
    headers?: Request["headers"]
  ) {
    return await this.transactionFactory.run(async (transaction) => {
      const assetTemplate =
        await this.assetTemplateService.findByTemplateAndTypeId(
          assetTemplateId,
          asset.assetTypeId
        );
      const datasources = await this.dataSourceService.getAll();
      const calculateDatasourceId = datasources.find(
        (datasource) => datasource.name === "Calculation"
      );
      if (assetTemplate === null) {
        throw new InvalidInputException("Asset template not found");
      }
      const newAsset = await this.assetService.create(
        {
          ...asset,
          assetTemplateId: assetTemplate.id,
          unitsGroupId: Number(unitsGroupId),
        },
        runById
      );
      await transaction.commit();
      const newMeasurements = await Promise.all(
        assetTemplate.measurements
          .getItems()
          .map(async (measurementTemplate) => {
            if (!measurementTemplate.metric) {
              throw new InvalidInputException(
                "Cannot create template instance with no metric"
              );
            }

            const metricId = measurementTemplate.metric.id;
            const overrides = measurementOverrides.get(metricId);

            let unitOfMeasure: UnitOfMeasure | null = null;
            if (overrides?.unitOfMeasureId !== undefined) {
              if (overrides?.showAllUnits) {
                // Allow any unit, ignore group
                unitOfMeasure = await this.unitOfMeasureService.getById(overrides.unitOfMeasureId);
                if (unitOfMeasure === null) {
                  throw new InvalidInputException(
                    "Unit of measure not found for given ID"
                  );
                }
              } else {
                // Restrict to group
                unitOfMeasure =
                  await this.unitsGroupService.findUnitByIdAndMeasurementType(
                    unitsGroupId,
                    overrides.unitOfMeasureId,
                    measurementTemplate.measurementType.id
                  );
                if (unitOfMeasure === null) {
                  throw new InvalidInputException(
                    "Unit of measure for given group and measurement type not found"
                  );
                }
              }
            } else {
              unitOfMeasure =
                await this.unitsGroupService.findMeasurementTypeDefaultUnit(
                  unitsGroupId,
                  measurementTemplate.measurementType.id
                );
            }

            const newAssetMeasurement =
              await this.assetMeasurementService.create(
                {
                  metricId,
                  tag: overrides?.tag ?? measurementTemplate.metric.name,
                  typeId: measurementTemplate.measurementType.id,
                  assetId: newAsset.id,
                  dataTypeId: measurementTemplate.dataType.id,
                  valueTypeId: measurementTemplate.valueType.id,
                  meterFactor:
                    overrides?.meterFactor ?? measurementTemplate.meterFactor,
                  locationId:
                    overrides?.locationId ?? measurementTemplate.location?.id,
                  unitOfMeasureId: unitOfMeasure?.id,
                  description:
                    overrides?.description ?? measurementTemplate.description,
                  datasourceId:
                    overrides?.datasourceId ??
                    measurementTemplate.datasource?.id,
                  writeback: false,
                },
                asset.customerId,
                runById,
                headers
              );

            if (!this.assertHasMetric(newAssetMeasurement)) {
              throw new Error("Asset measurement instance must have a metric");
            }
            return newAssetMeasurement;
          })
      );

      const assetMeasurementWithNonCalculateDatasource = newMeasurements.filter(
        (measurement) => measurement.datasourceId !== calculateDatasourceId?.id
      );
      const calcMeasurements = [];
      if (
        newMeasurements.filter(
          (measure) => measure.datasourceId === calculateDatasourceId?.id
        ).length > 0
      ) {
        for (const measurement of newMeasurements.filter(
          (measure) => measure.datasourceId === calculateDatasourceId?.id
        )) {
          if (measurement.datasourceId === calculateDatasourceId?.id) {
            const calculationMetricInstance =
              await this.calcMetricInstanceRepository.findOne({
                assetTemplate: { id: assetTemplate.id },
                outputMetric: measurement.metricId,
              });

            if (!calculationMetricInstance) continue;

            // Create CalculationInstance
            const calculationTemplateInstance = new CalculationInstance();
            calculationTemplateInstance.calculation =
              calculationMetricInstance.calculation;
            calculationTemplateInstance.createdby = runById;
            calculationTemplateInstance.created = new Date();
            calculationTemplateInstance.ispersisted =
              calculationMetricInstance.ispersisted;
            calculationTemplateInstance.pollPeriod =
              calculationMetricInstance.pollPeriod;

            // Find output measurement
            const measurementData = await this.measurementRepository.findOne({
              id: measurement.measurementId,
            });
            if (!measurementData) {
              throw new Error(
                `Measurement with ID ${measurement.id} not found`
              );
            }

            calculationTemplateInstance.outputMeasurement = measurementData;
            await this.calcInstanceRepository.persistAndFlush(
              calculationTemplateInstance
            );

            // Find inputs
            const calculationMetricInputs =
              await this.calculationMetricInput.find({
                calculationMetricInstance: { id: calculationMetricInstance.id },
                // metric: { id: measurement.metricId },
              });

            if (
              !calculationMetricInputs ||
              calculationMetricInputs.length === 0
            ) {
              throw new Error(
                `CalculationMetricInput not found for assetTemplate ID ${assetTemplate.id}`
              );
            }

            for (const input of calculationMetricInputs) {
              const calculationInput = new CalculationInput();
              calculationInput.inputLabel = input.inputLabel;
              if (input.metric !== null) {
                const calculatedMeasure =
                  assetMeasurementWithNonCalculateDatasource.find(
                    (measure) => measure.metricId === input.metric.id
                  );

                if (!calculatedMeasure) {
                  throw new Error(
                    `Input metric ${input.metric.id} not found in non-calculate datasource`
                  );
                }
                const inputMeasurementData =
                  await this.measurementRepository.findOne({
                    id: calculatedMeasure.measurementId,
                  });

                if (!inputMeasurementData) {
                  throw new Error(
                    `Measurement with ID ${calculatedMeasure.id} not found`
                  );
                }
                calculationInput.measurement = inputMeasurementData;
              } else {
                calculationInput.constantNumber = input.constantNumber;
                calculationInput.constantString = input.constantString;
              }
              calculationInput.comment = input.comment;
              calculationInput.createdby = runById;
              calculationInput.created = new Date();
              calculationInput.calculationInstance =
                calculationTemplateInstance;

              await this.calculationInputRepo.persistAndFlush(calculationInput);
              calcMeasurements.push(calculationInput);
            }
          }
        }
      }
      return {
        unitsGroupId,
        asset: newAsset,
        measurements: newMeasurements,
      };
    });
  }
}
