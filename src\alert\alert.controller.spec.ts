import { Test, TestingModule } from "@nestjs/testing";
import authConfiguration from "src/authentication/auth.config";
import { CsrfGuard } from "../authentication/infra/csrf.guard";
import { JwtAuthGuard } from "../authentication/infra/jwt-auth.guard";
import { RolesGuard } from "../authorization/infra/roles.guard";
import { AlertController } from "./alert.controller";
import { AlertService } from "./alert.service";

describe("AlertController", () => {
  let controller: AlertController;
  let service: AlertService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AlertController],
      providers: [
        {
          provide: AlertService,
          useValue: {
            getAllEvents: jest.fn(),
          },
        },
        {
          provide: authConfiguration.KEY,
          useValue: {
            activeCustomerKeyId: "customerId",
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn().mockReturnValue(true) })
      .overrideGuard(CsrfGuard)
      .useValue({ canActivate: jest.fn().mockReturnValue(true) })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: jest.fn().mockReturnValue(true) })
      .compile();

    controller = module.get<AlertController>(AlertController);
    service = module.get<AlertService>(AlertService);
  });

  describe("getAllEvents", () => {
    it("should call service with correct params and return result", async () => {
      const mockHeaders = { customerId: 123 };
      const mockStart = "100";
      const mockEnd = "200";
      const mockResult = [{ id: 1 }];
      (service.getAllEvents as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.getAllEvents(
        mockHeaders as any,
        mockStart,
        mockEnd
      );

      expect(service.getAllEvents).toHaveBeenCalledWith({
        customerId: 123,
        start: 100,
        end: 200,
      });
      
      expect(result).toEqual(mockResult);

    });

    it("should handle string numbers for start/end", async () => {
      const mockHeaders = { customerId: 5 };
      (service.getAllEvents as jest.Mock).mockResolvedValue("ok");

      const result = await controller.getAllEvents(
        mockHeaders as any,
        "10",
        "20"
      );

      expect(service.getAllEvents).toHaveBeenCalledWith({
        customerId: 5,
        start: 10,
        end: 20,
      });
      expect(result).toBe("ok");
    });
  });

  describe("createAlert", () => {
    it("should create alert and return 200 status code", async () => {
      const mockAlert = { name: "Test Alert" };
      const mockHeaders = { customerId: 123 };
      const mockUser = { id: 1 };
      const mockResult = { id: 1, ...mockAlert };
      
      service.createAlert = jest.fn().mockResolvedValue(mockResult);

      const result = await controller.createAlert(
        mockAlert as any,
        mockHeaders as any,
        { user: mockUser }
      );

      expect(service.createAlert).toHaveBeenCalledWith(mockAlert, mockHeaders, mockUser);
      expect(result).toEqual({
        message: "Alert Created Successfully",
        data: mockResult
      });
    });
  });
});
