import { EntityRepository, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { Injectable, NotFoundException } from "@nestjs/common";
import { InvalidInputException } from "src/errors/exceptions";
import { User } from "src/users/domain/user.entity";
import { isValidNameId } from "src/validations/validators";
import { Customer, CustomerId } from "./domain/customer.entity";
import {
  CustomerUpdateDto,
  GetAllCustomersDto,
} from "./dto/customer-creation.dto";
import { CustomerDto } from "./dto/customer-response.dto";
import { CustomerDefaultDashboard } from "src/dashboards/domain/customer-default-dashboard.entity";
import { CustomerFavoriteDashboard } from "src/dashboards/domain/customer-favorite-dashboard.entity";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";

export type CustomerCreationData = Omit<
  Customer,
  "id" | "enabled" | "createdAt" | "updatedAt" | "createdBy" | "updatedBy"
>;

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    private readonly customersRepository: EntityRepository<Customer>,

    @InjectRepository(CustomerDefaultDashboard)
    private readonly customerDefaultDashboardRepo: EntityRepository<CustomerDefaultDashboard>,

    @InjectRepository(CustomerFavoriteDashboard)
    private readonly customerFavoriteDashboardRepo: EntityRepository<CustomerFavoriteDashboard>
  ) {}

  async create(customer: CustomerCreationData, createdById: number) {
    if (!isValidNameId(customer.nameId)) {
      throw new InvalidInputException("Invalid customer name id");
    }

    if (
      (await this.customersRepository.count({ nameId: customer.nameId })) !== 0
    ) {
      throw new InvalidInputException(
        `Customer with name id "${customer.nameId}" already exists`
      );
    }

    const newCustomer = this.customersRepository.create({
      ...customer,
      enabled: true,
      nameId: customer.nameId.toLowerCase(),
      createdBy: Reference.createFromPK(User, createdById),
    });
    await this.customersRepository.persistAndFlush(newCustomer);

    return newCustomer;
  }

  async update(
    customerId: CustomerId,
    customerDto: CustomerUpdateDto,
    createdById: number
  ) {
    const customer = await this.customersRepository.findOne({ id: customerId });

    if (customer === null) {
      throw new NotFoundException("Customer does not exist");
    }

    if (customerDto.name) {
      customer.name = customerDto.name;
    }

    if (customerDto.address) {
      customer.address = customerDto.address;
    }

    if (customerDto.logo) {
      customer.logo = customerDto.logo;
    }

    customer.updatedAt = new Date();
    customer.updatedBy = Reference.createFromPK(User, createdById);

    await this.customersRepository.persistAndFlush(customer);

    if (customerDto.customerDefaultDashboardId !== undefined) {
      const existingDefault = await this.customerDefaultDashboardRepo.findOne({
        customer: customer.id,
      });

      // ✅ Handle null case: remove default dashboard if present
      if (customerDto.customerDefaultDashboardId === null) {
        if (existingDefault) {
          await this.customerDefaultDashboardRepo.removeAndFlush(
            existingDefault
          );
        }
      } else {
        // ✅ Handle valid ID: update or insert
        const dashboardRef = Reference.createFromPK(
          Dashboard,
          customerDto.customerDefaultDashboardId
        );

        if (existingDefault) {
          existingDefault.dashboard = dashboardRef;
        } else {
          const newDefault = this.customerDefaultDashboardRepo.create({
            customer: Reference.create(customer),
            dashboard: dashboardRef,
          });
          await this.customerDefaultDashboardRepo.persist(newDefault);
        }

        await this.customerDefaultDashboardRepo.flush();
      }
    }

    if (customerDto.customerFavoriteDashboardIds !== undefined) {
      await this.customerFavoriteDashboardRepo.nativeDelete({
        customer: customer.id,
      });

      const favRecords = customerDto.customerFavoriteDashboardIds.map(
        (dashboardId) =>
          this.customerFavoriteDashboardRepo.create({
            customer: Reference.create(customer),
            dashboard: Reference.createFromPK(Dashboard, dashboardId),
          })
      );
      await this.customerFavoriteDashboardRepo.persistAndFlush(favRecords);
    }

    return customer;
  }

  async getAll(filter: GetAllCustomersDto): Promise<CustomerDto[]> {
    const customers = await this.customersRepository.findAll({
      orderBy: { name: "ASC" },
      populate: filter.is_logo === "true",
    });

    const customerIds = customers.map((c) => c.id);

    // 🔹 Fetch default dashboards
    const defaultEntries = await this.customerDefaultDashboardRepo.find({
      customer: { $in: customerIds },
    });

    const defaultMap = new Map<number, number>();
    for (const entry of defaultEntries) {
      defaultMap.set(entry.customer.id, entry.dashboard.id);
    }

    // 🔹 Fetch favorite dashboards
    const favEntries = await this.customerFavoriteDashboardRepo.find({
      customer: { $in: customerIds },
    });

    const favMap = new Map<number, number[]>();
    for (const entry of favEntries) {
      const customerId = entry.customer.id;
      if (!favMap.has(customerId)) {
        favMap.set(customerId, []);
      }
      favMap.get(customerId)!.push(entry.dashboard.id);
    }

    // 🔹 Construct response
    return customers.map((c) => {
      const dto = new CustomerDto(c);
      dto.customerDefaultDashboardId = defaultMap.get(c.id) ?? null;
      dto.customerFavoriteDashboardIds = favMap.get(c.id) ?? [];
      return dto;
    });
  }

  async getAllById(
    ids: CustomerId[],
    filter: GetAllCustomersDto
  ): Promise<CustomerDto[]> {
    const customers = await this.customersRepository.find(
      { id: { $in: ids } },
      {
        orderBy: { name: "ASC" },
        populate: filter.is_logo === "true",
      }
    );

    const customerIds = customers.map((c) => c.id);

    // Fetch default dashboards
    const defaultEntries = await this.customerDefaultDashboardRepo.find({
      customer: { $in: customerIds },
    });
    const defaultMap = new Map<number, number>();
    for (const entry of defaultEntries) {
      defaultMap.set(entry.customer.id, entry.dashboard.id);
    }

    // Fetch favorite dashboards
    const favEntries = await this.customerFavoriteDashboardRepo.find({
      customer: { $in: customerIds },
    });
    const favMap = new Map<number, number[]>();
    for (const entry of favEntries) {
      const customerId = entry.customer.id;
      if (!favMap.has(customerId)) {
        favMap.set(customerId, []);
      }
      favMap.get(customerId)!.push(entry.dashboard.id);
    }

    // Construct response
    return customers.map((c) => {
      const dto = new CustomerDto(c);
      dto.customerDefaultDashboardId = defaultMap.get(c.id) ?? null;
      dto.customerFavoriteDashboardIds = favMap.get(c.id) ?? [];
      return dto;
    });
  }

  async findById(id: number): Promise<Customer | null> {
    return await this.customersRepository.findOne({ id });
  }

  async findByNameId(nameId: string) {
    return await this.customersRepository.findOne({ nameId });
  }

  async allExistById(ids: CustomerId[]) {
    return (
      (await this.customersRepository.count({ id: { $in: ids } })) ===
      ids.length
    );
  }

  async getCustomerWithLogo(customerId: number) {
    return await this.customersRepository.findOne(
      { id: customerId },
      { populate: ["logo"] }
    );
  }
}
