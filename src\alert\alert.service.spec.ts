import { AlertService } from "./alert.service";

describe("AlertService", () => {
  let service: AlertService;
  let alertsStatsService: any;
  let eventsRepo: any;
  let alertRepo: any;

  beforeEach(() => {
    alertsStatsService = {
      getRangeGroupedStats: jest.fn(),
    };
    eventsRepo = {
      find: jest.fn(),
    };
    alertRepo = {
      findOne: jest.fn(),
    };
    service = Object.create(AlertService.prototype);
    Object.defineProperty(service, "alertsStatsService", {
      value: alertsStatsService,
      writable: true,
    });
    Object.defineProperty(service, "events", {
      value: eventsRepo,
      writable: true,
    });
    Object.defineProperty(service, "alert", {
      value: alertRepo,
      writable: true,
    });
  });

  describe("getAllEvents", () => {
    it("should return excursionData, total, and mapped items", async () => {
      const customerId = 1;
      const start = 1000;
      const end = 2000;
      const excursionData = [{ foo: "bar" }];
      const events = [
        {
          alert_id: {
            measurement: { id: 10, _tag: "mTag" },
            asset: { id: 20, tag: "aTag" },
          },
        },
        {
          alert_id: {
            measurement: { id: 11, _tag: "mTag2" },
            asset: { id: 21, tag: "aTag2" },
          },
        },
      ];

      alertsStatsService.getRangeGroupedStats.mockResolvedValue(excursionData);
      eventsRepo.find.mockResolvedValue(events);

      const result = await service.getAllEvents({ customerId, start, end });

      expect(alertsStatsService.getRangeGroupedStats).toHaveBeenCalledWith(
        customerId,
        start,
        end
      );
      expect(eventsRepo.find).toHaveBeenCalledWith(
        {
          alert_id: { customerId },
          timestamp: { $gte: new Date(start), $lte: new Date(end) },
        },
        expect.objectContaining({
          orderBy: { timestamp: "DESC" },
          populate: expect.any(Array),
        })
      );
      expect(result).toEqual({
        excursionData,
        total: events.length,
        items: [
          {
            ...events[0],
            measurement: { id: 10, tag: "mTag" },
            asset: { id: 20, tag: "aTag" },
          },
          {
            ...events[1],
            measurement: { id: 11, tag: "mTag2" },
            asset: { id: 21, tag: "aTag2" },
          },
        ],
      });
    });

    it("should return empty items if no events", async () => {
      alertsStatsService.getRangeGroupedStats.mockResolvedValue([]);
      eventsRepo.find.mockResolvedValue([]);

      const result = await service.getAllEvents({
        customerId: 2,
        start: 1,
        end: 2,
      });

      expect(result).toEqual({
        excursionData: [],
        total: 0,
        items: [],
      });
    });
    it("should return getAlert with staleBand", async () => {
      const alertData = {
        id: 1,
        name: "Test Alert",
        anomalyParameter: {
          learningPeriod: 30,
          includeVelocity: true,
          includeMomentum: false,
        },
        staleBand: 5,
        alertUsers: [],
        measurement: { id: null, tag: "" },
        asset: "",
        customerId: "",
        description: "",
        agg: "",
        condition: null,
        notificationType: 0,
        period: "",
        resetDeadband: null,
        thresholdType: "",
        thresholdValue: null,
      };
      const headers = { "x-customer-id": "1" };
      alertRepo.findOne.mockResolvedValue(alertData);

      const result = await service.getAlert(1, false);

      expect(alertRepo.findOne).toHaveBeenCalledWith(1, {
        populate: ["measurement", "alertUsers", "anomalyParameter"],
      });
      expect(result).toEqual({
        agg: "",
        alertUsers: [],
        asset: "",
        condition: null,
        customerId: "",
        description: "",
        id: 1,
        includeMomentum: false,
        includeVelocity: true,
        learningPeriod: 30,
        measurement: {
          id: null,
          tag: "",
        },
        notificationType: 0,
        period: "",
        resetDeadband: null,
        staleBand: 5,
        thresholdType: "",
        thresholdValue: null,
      });
    });
  });
});
