import { MikroOrmModule } from "@mikro-orm/nestjs";
import { Module, forwardRef } from "@nestjs/common";
import { AuthModule } from "src/authentication/auth.module";
import { CustomersApiController } from "./customers.controller";
import { CustomerService } from "./customer.service";
import { Customer } from "./domain/customer.entity";
import { CustomerDefaultDashboard } from "src/dashboards/domain/customer-default-dashboard.entity";
import { CustomerFavoriteDashboard } from "src/dashboards/domain/customer-favorite-dashboard.entity";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      Customer,
      CustomerDefaultDashboard,
      CustomerFavoriteDashboard,
    ]),
    forwardRef(() => AuthModule),
  ],
  providers: [CustomerService],
  controllers: [CustomersApiController],
  exports: [CustomerService],
})
export class CustomersModule {}
