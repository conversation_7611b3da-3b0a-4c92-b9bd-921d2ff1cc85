import { Customer } from "../domain/customer.entity";

export class CustomerDto {
  id: number;
  nameId: string;
  name: string;
  address: string;
  enabled?: boolean;
  logo?: string;
  customerDefaultDashboardId?: number | null;
  customerFavoriteDashboardIds?: number[];

  constructor(customer: Customer) {
    this.id = customer.id;
    this.nameId = customer.nameId;
    this.name = customer.name;
    this.address = customer.address;
    this.enabled = customer.enabled;
    this.logo = customer.logo;
  }
}
