import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';
import { AlertService } from '../alert.service';
import { getRepositoryToken } from '@mikro-orm/nestjs';
import { Alerts } from '../domain/alert.entity';
import { AlertCondition } from '../domain/alertCondition.entity';
import { Aggregates } from '../domain/aggregates.entity';
import { AlertThresholdType } from '../domain/alertThresholdType.entity';
import { Measurement } from '../../measurements/domain/measurement.entity';
import { Asset } from '../../assets/domain/asset.entity';
import { Periods } from '../domain/periods.entity';
import { AlertUsers } from '../domain/alertUser.entity';
import { User } from '../../users/domain/user.entity';
import { Customer } from '../../customers/domain/customer.entity';
import { AlertRepository } from '../repository/alert.repository';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { TransactionFactory } from '../../db/TransactionFactory';
import { Events } from '../domain/events.entity';
import { AssetMeasurement } from '../../measurements/domain/asset-measurement.entity';
import { AnomalyModel } from '../domain/anomalyModel.entity';
import { Logger } from '@nestjs/common';
import { EntityManager } from '@mikro-orm/core';
import { DataType } from '../../measurements/domain/data-type.entity';
import { MeasurementType } from '../../measurements/domain/measurement-type.entity';
import { ValueType } from '../../measurements/domain/value-type.entity';
import { AlertDTO } from '../dto/alert.dto';

describe('AlertService', () => {
  let service: AlertService;
  let module: TestingModule;
  let alertRepository: jest.Mocked<any>;
  let transactionFactory: jest.Mocked<TransactionFactory>;

  beforeEach(async () => {
    const mockEntityManager = {
      persistAndFlush: jest.fn(),
      commit: jest.fn(),
    };

    const mockHttpService = {
      post: jest.fn().mockImplementation(() => of({ data: {} }))
    };

    // Mock all required repositories
    const mockRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      persistAndFlush: jest.fn(),
    };

    transactionFactory = {
      run: jest.fn((callback) => callback(mockEntityManager)),
    } as any;

    module = await Test.createTestingModule({
      providers: [
        AlertService,
        {
          provide: getRepositoryToken(Alerts),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(AlertCondition),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Aggregates),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(AlertThresholdType),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Measurement),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Asset),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Periods),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(AlertUsers),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Customer),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Events),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(AssetMeasurement),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(AnomalyModel),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(DataType),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(MeasurementType),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(ValueType),
          useValue: mockRepository,
        },
        {
          provide: AlertRepository,
          useValue: mockRepository,
        },
        {
          provide: ConfigService,
          useValue: { get: jest.fn() },
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: TransactionFactory,
          useValue: transactionFactory,
        },
        {
          provide: Logger,
          useValue: { log: jest.fn(), error: jest.fn() },
        },
        {
          provide: EntityManager,
          useValue: mockEntityManager,
        },
      ],
    }).compile();

    service = module.get<AlertService>(AlertService);
    alertRepository = module.get(getRepositoryToken(Alerts));
  });

  describe('createAlert', () => {
    it('should create an alert with staleBand value', async () => {
      // Mock dependencies
      const mockAgg = { id: 1, value: 'AVG' };
      const mockCustomer = { id: 1 };
      const mockThresholdType = { id: 1 };
      const mockMeasurement = { id: 1 };
      const mockAsset = { id: 1 };
      const mockPeriod = { id: 1, label: '5MIN' };
      const mockUsers = [{ id: 1 }];

      // Mock repositories
      [
        { repo: Aggregates, value: mockAgg },
        { repo: Customer, value: mockCustomer },
        { repo: AlertThresholdType, value: mockThresholdType },
        { repo: Measurement, value: mockMeasurement },
        { repo: Asset, value: mockAsset },
        { repo: Periods, value: mockPeriod }
      ].forEach(({ repo, value }) => {
        const repository = module.get(getRepositoryToken(repo));
        jest.spyOn(repository, 'findOne').mockResolvedValue(value);
      });

      // Mock user repository find method
      jest.spyOn(module.get<any>(getRepositoryToken(User)), 'find')
        .mockResolvedValue(mockUsers);

      // Create alert DTO with staleBand
      const alertDto: AlertDTO = {
        id: 1,
        measurement: 1,
        asset: 1,
        agg: 1,
        period: 1,
        thresholdType: 1,
        description: 'Test Alert',
        customerId: 1,
        users: [{ id: 1, notificationType: 1 }],
        staleBand: 5.5
      };

      // Create mock headers for Request
      const mockHeaders: Record<string, string> = {
        'be-csrftoken': 'test-token',
        'cookie': 'test-cookie'
      };

      // Call createAlert
      await service.createAlert(alertDto, mockHeaders as any, {});

      // Verify persistAndFlush was called with correct staleBand value
      const persistCall = alertRepository.persistAndFlush.mock.calls[0][0];
      expect(persistCall).toBeDefined();
      expect(persistCall.staleBand).toBe(5.5);
    });
  });
});