import { Migration } from '@mikro-orm/migrations';

export class Migration20250728070606_nullable_customer_upload_image extends Migration {

  async up(): Promise<void> {
    this.addSql('alter table "custom_image" drop constraint "custom_image_customer_foreign";');

    this.addSql('alter table "custom_image" alter column "customer" type int using ("customer"::int);');
    this.addSql('alter table "custom_image" alter column "customer" drop not null;');
    this.addSql('alter table "custom_image" add constraint "custom_image_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade on delete set null;');
  }

  async down(): Promise<void> {
    this.addSql('alter table "custom_image" drop constraint "custom_image_customer_foreign";');

    this.addSql('alter table "custom_image" alter column "customer" type int using ("customer"::int);');
    this.addSql('alter table "custom_image" alter column "customer" set not null;');
    this.addSql('alter table "custom_image" add constraint "custom_image_customer_foreign" foreign key ("customer") references "customer" ("id") on update cascade;');
  }

}
