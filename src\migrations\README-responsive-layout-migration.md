# Dashboard Responsive Layout Migration

## Overview

This migration adds responsive layout support to both the dashboard and dashboard template systems, allowing different widget layouts for desktop and mobile views.

## Migrations

### 1. Dashboard Migration: `20250604151745-add-responsive-layouts-to-dashboard.ts`
### 2. Dashboard Template Migration: `20250605163019-add-responsive-layouts-to-dashboard-template.ts`

### What they do

Both migrations perform identical transformations on their respective entities:

1. **Adds new properties to dashboard/dashboard template data JSON:**
   - `desktopMobile: 0 | 1` - Current device mode (0 = desktop, 1 = mobile)
   - `responsiveLayouts` - Object containing separate layouts for desktop and mobile

2. **Preserves existing data:**
   - Keeps the original `widget.widgetLayout` array for backward compatibility
   - Copies current layout to both desktop and mobile layouts initially

3. **Safe migration:**
   - Handles malformed JSON gracefully
   - Skips already migrated records
   - Provides detailed logging
   - Supports rollback

### Target Tables
- **Dashboard Migration:** `dashboard` table
- **Dashboard Template Migration:** `dashboard_template` table

### New Data Structure

**Before Migration:**
```json
{
  "widget": {
    "widgets": [...],
    "widgetLayout": [
      { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
      { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
    ]
  },
  "topPanel": {...},
  "chart": {...}
}
```

**After Migration:**
```json
{
  "widget": {
    "widgets": [...],
    "widgetLayout": [
      { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
      { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
    ]
  },
  "topPanel": {...},
  "chart": {...},
  "desktopMobile": 0,
  "responsiveLayouts": {
    "desktop": {
      "widgetLayout": [
        { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
        { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
      ]
    },
    "mobile": {
      "widgetLayout": [
        { "i": "widget-1", "x": 0, "y": 0, "w": 6, "h": 4 },
        { "i": "widget-2", "x": 6, "y": 0, "w": 6, "h": 4 }
      ]
    }
  }
}
```

## Type Definitions

New TypeScript types have been added to `src/dashboards/domain/dashboard.types.ts`:

- `Layout` - React-grid-layout compatible layout object
- `DeviceMode` - Device mode type (0 | 1)
- `ResponsiveLayouts` - Structure for responsive layouts
- `DashboardState` - Complete dashboard data structure
- `DashboardTemplateState` - Dashboard template data structure (alias of DashboardState)

## Running the Migrations

```bash
# Run both migrations
npm run migration:up

# Rollback if needed
npm run migration:down
```

## Testing

Test scripts are provided to verify the migration logic:

```bash
cd src/migrations

# Test dashboard migration
npx ts-node test-responsive-layout-migration.ts

# Test dashboard template migration
npx ts-node test-dashboard-template-responsive-layout-migration.ts
```

## Backward Compatibility

- The original `widget.widgetLayout` property is preserved
- Existing code that reads this property will continue to work
- New code should use `responsiveLayouts.desktop.widgetLayout` or `responsiveLayouts.mobile.widgetLayout`

## Usage in Application Code

### For Dashboards
```typescript
import { DashboardState, DeviceMode } from 'src/dashboards/domain/dashboard.types';

// Parse dashboard data
const dashboardData: DashboardState = JSON.parse(dashboard.data);

// Get current device mode
const currentMode: DeviceMode = dashboardData.desktopMobile || 0;

// Get layout for current device
const currentLayout = currentMode === 0
  ? dashboardData.responsiveLayouts?.desktop.widgetLayout
  : dashboardData.responsiveLayouts?.mobile.widgetLayout;

// Fallback to legacy layout if responsive layouts not available
const layout = currentLayout || dashboardData.widget.widgetLayout;
```

### For Dashboard Templates
```typescript
import { DashboardTemplateState, DeviceMode } from 'src/dashboards/domain/dashboard.types';

// Parse dashboard template data
const dashboardTemplateData: DashboardTemplateState = JSON.parse(dashboardTemplate.data);

// Get current device mode
const currentMode: DeviceMode = dashboardTemplateData.desktopMobile || 0;

// Get layout for current device
const currentLayout = currentMode === 0
  ? dashboardTemplateData.responsiveLayouts?.desktop.widgetLayout
  : dashboardTemplateData.responsiveLayouts?.mobile.widgetLayout;

// Fallback to legacy layout if responsive layouts not available
const layout = currentLayout || dashboardTemplateData.widget.widgetLayout;
```

## Error Handling

Both migrations include comprehensive error handling:
- Skips records with invalid JSON
- Logs detailed error messages
- Continues processing other records if one fails
- Provides summary of successful vs failed migrations

## Rollback Support

Both migrations can be safely rolled back:
- Removes `desktopMobile` and `responsiveLayouts` properties
- Preserves original `widget.widgetLayout` data
- No data loss during rollback

## Migration Order

The migrations can be run in any order since they target different tables:
1. `20250604151745-add-responsive-layouts-to-dashboard.ts` (dashboards)
2. `20250605163019-add-responsive-layouts-to-dashboard-template.ts` (dashboard templates)

Both are independent and can be run separately if needed.
