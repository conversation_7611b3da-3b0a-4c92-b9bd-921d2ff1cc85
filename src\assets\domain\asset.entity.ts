import {
  Collection,
  Entity,
  Filter,
  ManyToOne,
  OneToMany,
  PrimaryKey,
  Property,
  Ref,
  Reference,
} from "@mikro-orm/core";
import { AssetTemplate } from "src/asset-template/domain/asset-template.entity";
import { Customer, CustomerId } from "../../customers/domain/customer.entity";
import { InvalidInputException } from "../../errors/exceptions";
import { User, UserId } from "../../users/domain/user.entity";
import { AssetHierarchy } from "./asset-hierarchy.entity";
import { AssetType, AssetTypeId } from "./asset-type.entity";
import { UnitsGroup } from "src/measurements/domain/units-group.entity";

export type AssetId = number;

export type AssetCreationParams = Omit<
  Asset,
  | "id"
  | "customer"
  | "assetType"
  | "enabled"
  | "parentHierarchies"
  | "parentIds"
  | "childHierarchies"
  | "childrenIds"
  | "createdById"
  | "createdAt"
  | "updatedAt"
  | "updatedBy"
  | "deletedAt"
  | "deletedById"
>;

export const AssetFactory = {
  create(params: AssetCreationParams): Asset {
    const {
      tag,
      assetTypeId,
      customerId,
      timeZone,
      description,
      latitude,
      longitude,
      assetTemplateId,
      unitsGroupId,
    } = params;

    const asset = new Asset(
      tag,
      assetTypeId,
      customerId,
      timeZone,
      description,
      latitude,
      longitude,
      assetTemplateId,
      unitsGroupId
    );

    return asset;
  },
};
@Entity()
@Filter({
  name: "isNotDeleted",
  cond: { deletedAt: { $eq: null } },
  default: true,
})
@Filter({
  name: "isEnabled",
  cond: {
    $or: [{ enabled: true }, { enabled: null }],
  },
  default: true,
})
export class Asset {
  constructor(
    tag: string,
    assetTypeId: AssetTypeId,
    customerId: CustomerId,
    timeZone?: string,
    description?: string,
    latitude?: number,
    longitude?: number,
    assetTemplateId?: number,
    unitsGroupId?: number
  ) {
    if (!this.containsInvalidNameCharacters(this.tag)) {
      throw new InvalidInputException(
        "Only letters, numbers, and forward slashes are allowed and must start with a letter"
      );
    }
    this.tag = tag;
    this.enabled = true;
    this.assetType = Reference.createFromPK(AssetType, assetTypeId);
    this.assetTypeId = assetTypeId;
    if (assetTemplateId) {
      this.assetTemplate = Reference.createFromPK(
        AssetTemplate,
        assetTemplateId
      );
      this.assetTemplateId = assetTemplateId;
    }
    if (unitsGroupId) {
      this.unitsGroup = Reference.createFromPK(UnitsGroup, unitsGroupId);
      this.unitsGroupId = unitsGroupId;
    }
    this.customer = Reference.createFromPK(Customer, customerId);
    this.customerId = customerId;
    this.parentHierarchies = new Collection<AssetHierarchy>(this);
    this.childHierarchies = new Collection<AssetHierarchy>(this);
    this.timeZone = timeZone;
    this.description = description;
    this.latitude = latitude;
    this.longitude = longitude;
  }

  private containsInvalidNameCharacters(name: string) {
    return /^[a-zA-Z][a-zA-Z0-9/]*$/.test(name);
  }
  @PrimaryKey()
  id!: number;

  @Property({ length: 50 })
  tag!: string;

  @Property({ nullable: true, hidden: true })
  enabled?: boolean;

  @Property({
    columnType: "float4",
    nullable: true,
  })
  latitude?: number;

  @Property({
    columnType: "float4",
    nullable: true,
  })
  longitude?: number;

  @ManyToOne({
    fieldName: "a_type",
    hidden: true,
    index: "asset_a_type_idx",
  })
  assetType!: Ref<AssetType>;

  @Property({
    fieldName: "a_type",
    serializedName: "type_id",
    persist: false,
  })
  assetTypeId!: AssetTypeId;

  @ManyToOne({
    nullable: true,
    entity: () => AssetTemplate,
    fieldName: "asset_template",
  })
  assetTemplate?: Ref<AssetTemplate>;

  @Property({
    nullable: true,
    persist: false,
    fieldName: "asset_template",
  })
  assetTemplateId?: number;

  @ManyToOne({
    nullable: true,
    entity: () => UnitsGroup,
    fieldName: "UnitsGroup",
  })
  unitsGroup?: Ref<UnitsGroup>;

  @Property({
    nullable: true,
    persist: false,
    fieldName: "UnitsGroup",
  })
  unitsGroupId?: number;

  @ManyToOne({ fieldName: "customer", hidden: true })
  customer!: Ref<Customer>;

  @Property({
    serializedName: "customer_id",
    fieldName: "customer",
    persist: false,
  })
  customerId!: CustomerId;

  @Property({ length: 100, nullable: true })
  description?: string;

  @OneToMany(() => AssetHierarchy, (assetHier) => assetHier.child, {
    eager: true,
    persist: false,
    hidden: true,
  })
  parentHierarchies!: Collection<AssetHierarchy>;

  @Property({ persist: false, serializedName: "parent_ids" })
  get parentIds() {
    return this.parentHierarchies
      .getItems()
      .map((assetHier) => assetHier.parent)
      .filter(
        (parent): parent is Ref<Asset> =>
          parent !== undefined && parent !== null
      )
      .map((parent) => parent.id);
  }

  @OneToMany(() => AssetHierarchy, (assetHier) => assetHier.parent, {
    persist: false,
    eager: true,
    hidden: true,
  })
  childHierarchies!: Collection<AssetHierarchy>;

  @Property({ persist: false, serializedName: "children_ids" })
  get childrenIds() {
    return this.childHierarchies
      .getItems()
      .map((assetHier) => assetHier.child)
      .map((child) => child.id);
  }

  @Property({
    fieldName: "timezone",
    columnType: "text",
    nullable: true,
    serializedName: "time_zone",
  })
  timeZone?: string;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "created_by" })
  createdBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "created_by" })
  createdById?: UserId;

  @ManyToOne({ hidden: true, nullable: true, fieldName: "updated_by" })
  updatedBy?: Ref<User>;

  @Property({ hidden: true, length: 6, nullable: true })
  deletedAt?: Date;

  @ManyToOne(() => User, {
    hidden: true,
    nullable: true,
    fieldName: "deleted_by",
  })
  deletedBy?: Ref<User>;

  @Property({ hidden: true, nullable: true, fieldName: "deleted_by" })
  deletedById?: UserId;
}
