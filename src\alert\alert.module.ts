import { MikroOrmModule } from "@mikro-orm/nestjs";
import { HttpModule } from "@nestjs/axios";
import { forwardR<PERSON>, <PERSON><PERSON>, Module } from "@nestjs/common";
import { AlertStatsModule } from "src/alert-stats/alert-stats.module";
import { Asset } from "src/assets/domain/asset.entity";
import { AuthModule } from "src/authentication/auth.module";
import { Customer } from "src/customers/domain/customer.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { MeasurementModule } from "src/measurements/measurement.module";
import { SecurityModule } from "src/security/security.module";
import { User } from "src/users/domain/user.entity";
import { AlertController } from "./alert.controller";
import { AlertService } from "./alert.service";
import { Aggregates } from "./domain/aggregates.entity";
import { Alerts } from "./domain/alert.entity";
import { AlertCondition } from "./domain/alertCondition.entity";
import { AlertThresholdType } from "./domain/alertThresholdType.entity";
import { AlertUsers } from "./domain/alertUser.entity";
import { AnomalyModel } from "./domain/anomalyModel.entity";
import { Events } from "./domain/events.entity";
import { Periods } from "./domain/periods.entity";
import { AlertRepository } from "./repository/alert.repository";

@Module({
  controllers: [AlertController],
  providers: [
    AlertService,
    AlertRepository,
    TransactionFactory,
    {
      provide: Logger,
      useValue: new Logger(AlertService.name),
    },
  ],
  imports: [
    MikroOrmModule.forFeature([
      Alerts,
      AlertThresholdType,
      Aggregates,
      AlertCondition,
      Periods,
      Measurement,
      Asset,
      AlertUsers,
      User,
      Customer,
      Events,
      AssetMeasurement,
      AnomalyModel,
    ]),
    MeasurementModule,
    SecurityModule,
    HttpModule,
    AlertStatsModule,
    forwardRef(() => AuthModule),
  ],
  exports: [AlertService, TransactionFactory],
})
export class AlertModule {}
