import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FilterQuery, Reference } from "@mikro-orm/core";
import { InjectRepository } from "@mikro-orm/nestjs";
import { EntityRepository } from "@mikro-orm/postgresql";
import { ConflictException, Logger, NotFoundException } from "@nestjs/common";
import { AssetService } from "src/assets/asset.service";
import { AssetType } from "src/assets/domain/asset-type.entity";
import { Metric } from "src/assets/domain/metric.entity";
import { MetricService } from "src/assets/metric.service";
import { AssetRepository } from "src/assets/repository/asset.repository";
import { CalculationInput } from "src/calc-engine/domain/calculation-input.entity";
import { CalculationInstance } from "src/calc-engine/domain/calculation-instance.entity";
import { CalcEngineRepository } from "src/calc-engine/repository/calc-engine.repository";
import { CalculationMetricInput } from "src/calc-metrics-controller/domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";
import { TransactionFactory } from "src/db/TransactionFactory";
import { AssetMeasurementService } from "src/measurements/asset-measurement.service";
import { DataType } from "src/measurements/domain/data-type.entity";
import { Datasource } from "src/measurements/domain/datasource.entity";
import { Location } from "src/measurements/domain/location.entity";
import { MeasurementType } from "src/measurements/domain/measurement-type.entity";
import { ValueType } from "src/measurements/domain/value-type.entity";
import { UnitsGroupService } from "src/measurements/units-group.service";
import { User, UserId } from "src/users/domain/user.entity";
import { AssetTemplateMeasurement } from "../domain/asset-template-measurement.entity";
import { AssetTemplate } from "../domain/asset-template.entity";
import { expressionInstanceDto } from "../dto/asset-template.dto";

export class AssetTemplateRepository {
  constructor(
    @InjectRepository(AssetTemplate)
    private readonly entityRepository: EntityRepository<AssetTemplate>,
    @InjectRepository(AssetTemplateMeasurement)
    private readonly assetTemplateMeasurementRepo: EntityRepository<AssetTemplateMeasurement>,
    @InjectRepository(Datasource)
    private readonly dataSource: EntityRepository<Datasource>,
    @InjectRepository(CalculationMetricInstance)
    private readonly calculationMetricInstance: EntityRepository<CalculationMetricInstance>,
    @InjectRepository(CalculationMetricInput)
    private readonly calculationMetricInputs: EntityRepository<CalculationMetricInput>,
    @InjectRepository(CalculationInstance)
    private calcInstanceRepository: EntityRepository<CalculationInstance>,
    @InjectRepository(CalculationInput)
    private calcInputRepository: EntityRepository<CalculationInput>,
    private readonly metricService: MetricService,
    private readonly calcEngineRepo: CalcEngineRepository,
    private readonly assetRepository: AssetRepository,
    private readonly asset: AssetService,
    private readonly assetMeasurementService: AssetMeasurementService,
    private readonly unitsGroupService: UnitsGroupService,
    private readonly transactionFactory: TransactionFactory,
    private readonly logger: Logger
  ) {}

  async add(
    assetTemplate: AssetTemplate,
    createdById: UserId
  ): Promise<AssetTemplate> {
    const creator = Reference.createFromPK(User, createdById);
    const creationDate = new Date();
    assetTemplate.createdBy = creator;
    assetTemplate.createdAt = creationDate;

    assetTemplate.measurements.getItems().forEach((measurement) => {
      measurement.createdBy = creator;
      measurement.createdAt = creationDate;
    });
    try {
      await this.entityRepository.persistAndFlush(assetTemplate);
    } catch (error: any) {
      if (error.code === "23505") {
        throw new Error(
          `Duplicate Asset Template: Manufacturer '${assetTemplate.manufacturer}' and Model No '${assetTemplate.modelNumber}' already exist.`
        );
      }
      throw error;
    }
    return assetTemplate;
  }

  async findById(
    id: number,
    filter: { assetTypeId?: number } = {}
  ): Promise<AssetTemplate | null> {
    const condition: FilterQuery<AssetTemplate> = filter.assetTypeId
      ? { id, assetType: { id: filter.assetTypeId } }
      : { id };

    return await this.entityRepository.findOne(condition);
  }

  async getAllByAssetTypeId(
    assetTypeId: number,
    customerId: number
  ): Promise<AssetTemplate[]> {
    return await this.entityRepository.find({
      assetType: { id: assetTypeId },
      $or: [
        {
          customer: null,
        },
        {
          customer: { id: customerId },
        },
      ],
    });
  }
  async getAssetTemplateWithAssetTypeAndId(
    assetTypeId: number,
    templateId: number
  ) {
    return await this.entityRepository.findOne({
      $and: [
        {
          assetType: { id: assetTypeId },
        },
        {
          id: templateId,
        },
      ],
    });
  }
  async getAssociatedAssets(assetTemplateId: number) {
    return await this.assetRepository.getAllByAssetTemplateId(assetTemplateId);
  }
  async updateAssetTemplate({
    assetTypeId,
    templateId,
    createdBy,
    updateData, // Contains manufacturer, modelNumber, assetTypeId, and measurement array
    headers,
    em,
  }: {
    createdBy: number;
    assetTypeId: AssetType;
    templateId: number;
    updateData: {
      manufacturer: string;
      modelNumber: string;
      assetTypeId: AssetType;
      measurement: {
        id?: number; // Optional: if present, update; if not, create new
        measurementType: MeasurementType;
        metric: Metric;
        dataType: DataType;
        valueType: ValueType;
        metricId: number;
        measurementTypeId: number;
        dataTypeId: number;
        valueTypeId: number;
        locationId?: number;
        location?: Location;
        datasource?: Datasource;
        datasourceId?: number;
        description?: string;
        meterFactor?: number;
      }[];
    };
    headers: Request["headers"];
    em?: EntityManager;
    expressionInstance?: expressionInstanceDto[];
  }) {
    return await this.transactionFactory.run(async (emanager) => {
      const entityManager = em ?? emanager;
      const assetTemplate = await this.entityRepository.findOne(
        {
          id: templateId,
        },
        {
          ctx: em,
        }
      );
      if (!assetTemplate) {
        this.logger.error("Asset template not found", {
          templateId,
          assetTypeId,
        });
        throw new NotFoundException("Asset template not found");
      }
      assetTemplate.manufacturer = updateData.manufacturer;
      assetTemplate.modelNumber = updateData.modelNumber;
      assetTemplate.assetType = assetTypeId;
      const existingMeasurements = await this.assetTemplateMeasurementRepo.find(
        {
          assetTemplate: { id: templateId },
        },
        {
          ctx: em,
        }
      );
      const metricMapping: Record<
        number,
        { oldMetric: Metric; newMetric?: Metric }
      > = {};

      for (const measurement of existingMeasurements) {
        metricMapping[measurement.metric?.id] = {
          oldMetric: measurement.metric,
        };
      }
      const updateMeasurements = updateData.measurement.filter((m) => m.id);
      const newMeasurementsData = updateData.measurement.filter((m) => !m.id);

      const measurementsToDelete = existingMeasurements.filter(
        (m) => !updateMeasurements.some((um) => um.id === m.id)
      );

      await this.assetTemplateMeasurementRepo.nativeDelete(
        measurementsToDelete.map((m) => m.id),
        {
          ctx: em,
        }
      );
      // Remove references in assetTemplate
      assetTemplate.measurements.remove(measurementsToDelete);
      for (const measurementData of updateMeasurements) {
        const measurementToUpdate = existingMeasurements.find(
          (m) => m.id === measurementData.id
        );
        if (measurementToUpdate) {
          measurementToUpdate.measurementType = measurementData.measurementType;
          measurementToUpdate.dataType = measurementData.dataType;
          measurementToUpdate.valueType = measurementData.valueType;
          measurementToUpdate.meterFactor = measurementData.meterFactor ?? null;
          measurementToUpdate.description = measurementData.description ?? null;
          measurementToUpdate.datasource = measurementData.datasource || null;
          measurementToUpdate.location = measurementData.location || null;
          if (measurementToUpdate.metric?.id) {
            metricMapping[measurementToUpdate.metric?.id] = {
              oldMetric: measurementToUpdate.metric,
              newMetric: measurementData?.metric,
            };
            measurementToUpdate.metric = measurementData.metric ?? null;
          }
        }
      }

      const creator = Reference.createFromPK(User, createdBy);
      const now = new Date();
      const newMeasurements = await Promise.all(
        newMeasurementsData.map(async (measurement) => {
          const entity = this.assetTemplateMeasurementRepo.create({
            ...measurement,
            assetTemplate,
          });
          entity.createdBy = creator;
          entity.createdAt = now;
          await this.assetTemplateMeasurementRepo.persistAndFlush(entity);
          return entity;
        })
      );

      assetTemplate.measurements.add(newMeasurements);

      await this.entityRepository.persistAndFlush(assetTemplate);
      const assetsAssociated =
        await this.assetRepository.getAllByAssetTemplateId(templateId);
      assetTemplate.measurements.getItems().forEach((m) => {
        const existingMeasurement = existingMeasurements.find(
          (em) => em.id === m.id
        );
        if (existingMeasurement) {
          metricMapping[existingMeasurement.metric.id] = {
            oldMetric: existingMeasurement.metric,
            newMetric: m?.metric,
          };
        }
      });
      if (assetsAssociated.length > 0) {
        await Promise.all(
          assetsAssociated.map(async (asset) => {
            const measurements = await this.assetMeasurementService.getAll(
              asset.customer?.id,
              asset.id
            );

            // Delete measurements with removed metrics
            const metricIdsToDelete = new Set(
              measurementsToDelete.map((m) => m.metric.id)
            );

            const assetMeasurementsToDelete = measurements.filter(
              (measurement) => metricIdsToDelete.has(measurement.metricId)
            );

            await Promise.all(
              assetMeasurementsToDelete.map((measurement) =>
                this.assetMeasurementService.removeById(
                  asset.customer?.id,
                  asset.id,
                  measurement.id,
                  createdBy,
                  headers,
                  em
                )
              )
            );

            // Update measurements with new metric mappings
            const assetMeasurementsToUpdate = measurements.filter(
              (measurement) => !metricIdsToDelete.has(measurement.metricId)
            );

            await Promise.all(
              assetMeasurementsToUpdate.map(async (assetMeasurement) => {
                const mapping = metricMapping[assetMeasurement.metricId];

                let updatedTag = assetMeasurement.tag;
                if (mapping?.newMetric?.name) {
                  updatedTag = updatedTag.includes("\\")
                    ? updatedTag.split("\\").slice(0, -1).join("\\") +
                      "\\" +
                      mapping.newMetric.name
                    : mapping.newMetric.name;
                }

                await this.assetMeasurementService.update(
                  asset.customer?.id,
                  asset.id,
                  assetMeasurement.id,
                  {
                    ...assetMeasurement,
                    metricId: mapping?.newMetric?.id ?? null,
                    tag: updatedTag,
                  },
                  createdBy,
                  headers,
                  em
                );
              })
            );

            // Insert new measurements
            await Promise.all(
              newMeasurementsData.map(async (measurement) => {
                const unitOfMeasure = asset.unitsGroupId
                  ? await this.unitsGroupService.findMeasurementTypeDefaultUnit(
                      asset.unitsGroupId,
                      measurement.measurementType.id
                    )
                  : null;
                await this.assetMeasurementService.create(
                  {
                    metricId: measurement.metric?.id,
                    tag: `${asset.tag}\\${measurement.metric.name}`,
                    typeId: measurement.measurementType.id,
                    assetId: asset.id,
                    dataTypeId: measurement.dataType.id,
                    valueTypeId: measurement.valueType.id,
                    meterFactor: measurement.meterFactor,
                    locationId: measurement.location?.id,
                    unitOfMeasureId: unitOfMeasure?.id,
                    description: measurement.description,
                    datasourceId: measurement.datasource?.id,
                    writeback: false,
                  },
                  asset.customerId,
                  createdBy,
                  headers,
                  em
                );
              })
            );
          })
        );
      }
    });
  }

  async updateAssetTemplateMulti({
    assetTypeId,
    templateId,
    createdBy,
    updateData, // Contains manufacturer, modelNumber, assetTypeId, and measurement array
    headers,
    em,
    expressionInstance,
  }: {
    createdBy: User;
    assetTypeId: AssetType;
    templateId: number;
    updateData: {
      manufacturer: string;
      modelNumber: string;
      assetTypeId: AssetType;
      measurement: {
        id?: number; // Optional: if present, update; if not, create new
        measurementType: MeasurementType;
        metric: Metric;
        dataType: DataType;
        valueType: ValueType;
        metricId: number;
        measurementTypeId: number;
        dataTypeId: number;
        valueTypeId: number;
        locationId?: number;
        location?: Location;
        datasource?: Datasource;
        datasourceId?: number;
        description?: string;
        meterFactor?: number;
      }[];
    };
    headers: Request["headers"];
    em?: EntityManager;
    expressionInstance?: expressionInstanceDto[];
  }) {
    return await this.transactionFactory.run(async (emanager) => {
      const entityManager = em ?? emanager;
      const assetTemplate = await this.entityRepository.findOne(
        {
          id: templateId,
        },
        {
          ctx: em,
        }
      );
      if (!assetTemplate) {
        this.logger.error("Asset template not found", {
          templateId,
          assetTypeId,
        });
        throw new NotFoundException("Asset template not found");
      }
      const calculated = await this.dataSource.findAll();
      assetTemplate.manufacturer = updateData.manufacturer;
      assetTemplate.modelNumber = updateData.modelNumber;
      assetTemplate.assetType = assetTypeId;
      const existingMeasurements = await this.assetTemplateMeasurementRepo.find(
        {
          assetTemplate: { id: templateId },
        },
        {
          ctx: em,
        }
      );
      const metricMapping: Record<
        number,
        { oldMetric: Metric; newMetric?: Metric }
      > = {};

      for (const measurement of existingMeasurements) {
        metricMapping[measurement.metric?.id] = {
          oldMetric: measurement.metric,
        };
      }
      const updateMeasurements = updateData.measurement.filter((m) => m.id);
      const newMeasurementsData = updateData.measurement.filter((m) => !m.id);

      const measurementsToDelete = existingMeasurements.filter(
        (m) => !updateMeasurements.some((um) => um.id === m.id)
      );

      await this.assetTemplateMeasurementRepo.nativeDelete(
        measurementsToDelete.map((m) => m.id),
        {
          ctx: em,
        }
      );
      // Remove references in assetTemplate
      assetTemplate.measurements.remove(measurementsToDelete);
      if (measurementsToDelete.length > 0) {
        // delete calc metrics instance where measusurements are deleted  and have insatnce
        const calcMetricsToDelete = await this.calculationMetricInstance.find({
          $and: [
            {
              assetTemplate: assetTemplate,
              outputMetric: {
                $in: measurementsToDelete.map((metric) => metric.metric.id),
              },
            },
          ],
        });

        const calculatedMetrics = await this.calculationMetricInputs.find(
          {
            // id:
            calculationMetricInstance: {
              $in: calcMetricsToDelete.map((item) => item.id),
            },
          },
          {
            ctx: em,
          }
        );
        await this.calculationMetricInputs.nativeDelete(
          {
            id: {
              $in: calculatedMetrics.map((ids) => ids.id),
            },
          },
          {
            ctx: em,
          }
        );
        await this.calculationMetricInstance.nativeDelete(
          {
            id: {
              $in: calcMetricsToDelete.map((item) => item.id),
            },
          },
          {
            ctx: em,
          }
        );
      }
      for (const measurementData of updateMeasurements) {
        const measurementToUpdate = existingMeasurements.find(
          (m) => m.id === measurementData.id
        );
        if (measurementToUpdate) {
          measurementToUpdate.measurementType = measurementData.measurementType;
          measurementToUpdate.dataType = measurementData.dataType;
          measurementToUpdate.valueType = measurementData.valueType;
          measurementToUpdate.meterFactor = measurementData.meterFactor ?? null;
          measurementToUpdate.description = measurementData.description ?? null;
          measurementToUpdate.datasource = measurementData.datasource || null;
          measurementToUpdate.location = measurementData.location || null;
          if (measurementToUpdate.metric?.id) {
            metricMapping[measurementToUpdate.metric?.id] = {
              oldMetric: measurementToUpdate.metric,
              newMetric: measurementData?.metric,
            };
            measurementToUpdate.metric = measurementData.metric ?? null;
          }
        }
      }

      const creator = Reference.createFromPK(User, createdBy.id);
      const now = new Date();
      const newMeasurements = await Promise.all(
        newMeasurementsData.map(async (measurement) => {
          const entity = this.assetTemplateMeasurementRepo.create({
            ...measurement,
            assetTemplate,
          });
          entity.createdBy = creator;
          entity.createdAt = now;
          await this.assetTemplateMeasurementRepo.persistAndFlush(entity);
          return entity;
        })
      );

      assetTemplate.measurements.add(newMeasurements);

      await this.entityRepository.persistAndFlush(assetTemplate);
      const assetsAssociated =
        await this.assetRepository.getAllByAssetTemplateId(templateId);
      assetTemplate.measurements.getItems().forEach((m) => {
        const existingMeasurement = existingMeasurements.find(
          (em) => em.id === m.id
        );
        if (existingMeasurement) {
          metricMapping[existingMeasurement.metric.id] = {
            oldMetric: existingMeasurement.metric,
            newMetric: m?.metric,
          };
        }
      });
      type CalcBundle = {
        instance: CalculationMetricInstance;
        inputs: CalculationMetricInput[];
      };
      // check metricmapping for new and update metrics has same metricId or not
      if (Object.keys(metricMapping).length > 0) {
        Object.values(metricMapping).forEach(async (mapping) => {
          if (
            mapping.newMetric &&
            mapping.oldMetric.id !== mapping.newMetric.id
          ) {
            // check old metric is used in any calculation metric instance
            const existingInstance = await em.findOne(
              CalculationMetricInstance,
              {
                outputMetric: mapping.oldMetric.id,
                assetTemplate,
              }
            );
            // check old metric is used in any calculation metric input
            if (existingInstance) {
              const existingInputs = await em.find(
                CalculationMetricInput,
                {
                  calculationMetricInstance: existingInstance,
                },
                { ctx: em }
              );
              // if existingInputs is not null then delete it
              if (existingInputs && existingInputs.length > 0) {
                em.remove(existingInputs);
              }
            }
            //if existingInstance is not null then delete it
            if (existingInstance) {
              em.remove(existingInstance);
            }
          }
        });
      }
      const expressionEntities: CalcBundle[] = [];
      if (expressionInstance && expressionInstance.length > 0) {
        // Check in metricMapping where old and new metric are different and new metric exists in expressionInstance metricId will be
        //  used to create new CalculationMetricInstance in new metricIdsfromMapping
        const newMetricIdsfromMapping = new Set(
          Object.values(metricMapping)
            .filter(
              (mapping) =>
                mapping.newMetric &&
                mapping.oldMetric.id !== mapping.newMetric.id &&
                expressionInstance.some(
                  (expression) =>
                    Number(expression.metricId) === mapping.newMetric!.id
                )
            )
            .map((mapping) => mapping.newMetric!.id)
        );
        const newMetricIds = new Set(newMeasurements.map((m) => m.metric?.id));
        newMetricIdsfromMapping.forEach((id) => newMetricIds.add(id));
        const existingToUpdateIds = new Set(
          updateMeasurements.map((m) => m.metric?.id)
        );
        for (const expression of expressionInstance) {
          const isNew = newMetricIds.has(Number(expression.metricId));
          const isUpdate = existingToUpdateIds.has(Number(expression.metricId));

          let instance: CalculationMetricInstance;
          const inputs: CalculationMetricInput[] = [];

          if (isNew) {
            instance = await em.create(CalculationMetricInstance, {
              assetTemplate: assetTemplate.id,
              outputMetric: expression.metricId,
              created: new Date(),
              createdby: createdBy.id,
              writeback: expression.iswriteback,
              pollPeriod: expression.pollPeriod,
              calculation: expression.templateId,
              ispersisted: expression.ispersisted,
            });

            for (const input of expression.variables) {
              const calcInput = em.create(CalculationMetricInput, {
                created: new Date(),
                createdby: createdBy.id,
                calculationMetricInstance: instance,
                inputLabel: input.inputLabel,
                comment: input.comment ?? "",
              });

              if (input.metric) {
                const metric = await this.metricService.findById(input.metric);
                if (!metric) {
                  throw new ConflictException(
                    `Measurement with ID ${input.metric} not found`
                  );
                }
                calcInput.metric = metric;
              } else {
                calcInput.constantNumber =
                  input.constantType === "number" ? input.constantValue : null;
                calcInput.constantString =
                  input.constantType === "string" ? input.constantValue : null;
              }

              inputs.push(calcInput);
            }
            await em.persistAndFlush(instance);
            await em.persistAndFlush(inputs);
            expressionEntities.push({ instance, inputs });
          } else if (isUpdate) {
            const existingInstance = await em.findOne(
              CalculationMetricInstance,
              {
                outputMetric: expression.metricId,
                assetTemplate,
              }
            );

            if (!existingInstance) {
              throw new ConflictException(
                `Calculation Metric Instance with ID ${expression.metricId} not found`
              );
            }

            existingInstance.writeback = expression.iswriteback;
            existingInstance.pollPeriod = expression.pollPeriod;
            existingInstance.ispersisted = expression.ispersisted;

            const existingInputs = await em.find(
              CalculationMetricInput,
              {
                calculationMetricInstance: existingInstance,
              },
              { ctx: em }
            );

            for (const variable of expression.variables) {
              const input = existingInputs.find(
                (i) => i.inputLabel === variable.inputLabel
              );
              if (input) {
                input.comment = variable.comment ?? "";

                if (variable.metric) {
                  const metric = await this.metricService.findById(
                    variable.metric
                  );
                  if (!metric) {
                    throw new ConflictException(
                      `Measurement with ID ${variable.metric} not found`
                    );
                  }
                  input.metric = metric;
                  input.constantNumber = null;
                  input.constantString = null;
                } else {
                  const isNumber =
                    variable.constantValue !== null &&
                    variable.constantValue !== undefined &&
                    !isNaN(Number(variable.constantValue));
                  input.constantNumber = isNumber
                    ? variable.constantValue
                    : null;
                  input.constantString =
                    !isNumber && variable.constantValue
                      ? variable.constantValue
                      : null;
                  input.metric = null;
                }
              }
            }

            expressionEntities.push({
              instance: existingInstance,
              inputs: existingInputs,
            });
          }
        }
      }
      if (assetsAssociated.length > 0) {
        const calculatedId = calculated.find(
          (calc) => calc.name === "Calculation"
        );
        await Promise.all(
          assetsAssociated.map(async (asset) => {
            const measurements = await this.assetMeasurementService.getAll(
              asset.customer?.id,
              asset.id
            );

            // Delete measurements with removed metrics
            const metricIdsToDelete = new Set(
              measurementsToDelete.map((m) => m.metric.id)
            );

            const assetMeasurementsToDelete = measurements.filter(
              (measurement) => metricIdsToDelete.has(measurement.metricId)
            );

            await Promise.all(
              assetMeasurementsToDelete.map((measurement) =>
                this.assetMeasurementService.removeById(
                  asset.customer?.id,
                  asset.id,
                  measurement.id,
                  createdBy.id,
                  headers,
                  em
                )
              )
            );

            // Update measurements with new metric mappings
            const assetMeasurementsToUpdate = measurements.filter(
              (measurement) => !metricIdsToDelete.has(measurement.metricId)
            );

            await Promise.all(
              assetMeasurementsToUpdate.map(async (assetMeasurement) => {
                const mapping = metricMapping[assetMeasurement.metricId];

                let updatedTag = assetMeasurement.tag;
                if (mapping?.newMetric?.name) {
                  updatedTag = updatedTag.includes("\\")
                    ? updatedTag.split("\\").slice(0, -1).join("\\") +
                      "\\" +
                      mapping.newMetric.name
                    : mapping.newMetric.name;
                }

                await this.assetMeasurementService.update(
                  asset.customer?.id,
                  asset.id,
                  assetMeasurement.id,
                  {
                    ...assetMeasurement,
                    metricId: mapping?.newMetric?.id ?? null,
                    tag: updatedTag,
                  },
                  createdBy.id,
                  headers,
                  em
                );
              })
            );
            // Insert new measurements
            const newAssetMeasures = await Promise.all(
              newMeasurementsData.map(async (measurement) => {
                const unitOfMeasure = asset.unitsGroupId
                  ? await this.unitsGroupService.findMeasurementTypeDefaultUnit(
                      asset.unitsGroupId,
                      measurement.measurementType.id
                    )
                  : null;
                const assetMeasure = await this.assetMeasurementService.create(
                  {
                    metricId: measurement.metric?.id,
                    tag: `${asset.tag}\\${measurement.metric.name}`,
                    typeId: measurement.measurementType.id,
                    assetId: asset.id,
                    dataTypeId: measurement.dataType.id,
                    valueTypeId: measurement.valueType.id,
                    meterFactor: measurement.meterFactor,
                    locationId: measurement.location?.id,
                    unitOfMeasureId: unitOfMeasure?.id,
                    description: measurement.description,
                    datasourceId: measurement.datasource?.id,
                    writeback: false,
                  },
                  asset.customerId,
                  createdBy.id,
                  headers,
                  em
                );
                return assetMeasure;
              })
            );
            const affectedMeasurements = [
              ...assetMeasurementsToUpdate,
              ...newAssetMeasures,
            ];
            const calculcatedMeasurements = [...affectedMeasurements].filter(
              (calculatedMeasurements) =>
                calculatedMeasurements.datasourceId === calculatedId.id
            );
            if (calculcatedMeasurements.length > 0) {
              const newAssetMeasuresIds = newAssetMeasures.map(
                (assetMeasure) => assetMeasure.id
              );
              const existingToUpdateIds = new Set(
                updateMeasurements.map((m) => m.metric?.id)
              );
              const expressionMap = new Map<number, CalcBundle>(); // metricId => { instance, inputs }

              for (const { instance, inputs } of expressionEntities) {
                if (instance.outputMetric) {
                  expressionMap.set(Number(instance.outputMetric.id), {
                    instance,
                    inputs,
                  });
                }
              }
              if (newAssetMeasuresIds.length > 0) {
                await Promise.all(
                  newAssetMeasures
                    .filter(
                      (
                        measure // create only new calc measurements only
                      ) => newAssetMeasuresIds.includes(measure.id)
                    )
                    .map(async (assetMeasure) => {
                      const customer =
                        assetMeasure.asset.getEntity().customerId;

                      const bundle = expressionMap.get(assetMeasure.metricId);
                      if (!bundle) return;

                      const {
                        instance: calculatedMetricsInstance,
                        inputs: calculatedInputs,
                      } = bundle;

                      await this.calcEngineRepo.createCalcInputs(
                        {
                          customerId: customer,
                          ispersisted: calculatedMetricsInstance.ispersisted,
                          iswriteback:
                            calculatedMetricsInstance.writeback ?? false,
                          outputMesurementId: assetMeasure.measurementId,
                          pollPeriod: calculatedMetricsInstance.pollPeriod,
                          templateId: calculatedMetricsInstance.calculation.id,
                          inputs: calculatedInputs.map((input) => {
                            const isNumber =
                              input.constantNumber !== null &&
                              input.constantNumber !== undefined &&
                              !isNaN(Number(input.constantNumber));

                            return {
                              inputLabel: input.inputLabel,
                              comment: input.comment,
                              inputId: input.id,
                              constantType: isNumber ? "number" : "string",
                              constantValue: input.constantNumber,
                              measurementId: input.metric?.id
                                ? affectedMeasurements.find(
                                    (measure) =>
                                      measure.metricId === input.metric!.id
                                  )?.measurementId ?? null
                                : null,
                            };
                          }),
                        },
                        createdBy,
                        headers,
                        customer,
                        em
                      );
                    })
                );
              }

              if (existingToUpdateIds.size > 0) {
                const measurementIdsToUpdate = assetMeasurementsToUpdate
                  .filter((m) => existingToUpdateIds.has(m.metricId))
                  .map((m) => m.measurementId);

                const calcInstances = await em.find(CalculationInstance, {
                  outputMeasurement: { $in: measurementIdsToUpdate },
                });

                const calcInputVars = await em.find(CalculationInput, {
                  calculationInstance: {
                    $in: calcInstances.map((ci) => ci.id),
                  },
                });

                // Map calculationInstance.id => CalculationInput[]
                const calcInputMap = new Map<number, CalculationInput[]>();
                for (const input of calcInputVars) {
                  const list =
                    calcInputMap.get(input.calculationInstance.id) || [];
                  list.push(input);
                  calcInputMap.set(input.calculationInstance.id, list);
                }

                const calcInstanceMap = new Map<number, CalculationInstance>();
                for (const instance of calcInstances) {
                  calcInstanceMap.set(instance.outputMeasurement.id, instance);
                }
                await Promise.all(
                  assetMeasurementsToUpdate
                    .filter(
                      (
                        measure // create only new calc measurements only
                      ) => existingToUpdateIds.has(measure.metricId)
                    )
                    .map(async (assetMeasure) => {
                      const customer =
                        assetMeasure.asset.getEntity().customerId;
                      const bundle = expressionMap.get(assetMeasure.metricId);
                      if (!bundle) return;

                      const {
                        instance: calcMetricInstance,
                        inputs: calcInputs,
                      } = bundle;

                      const calcInstance = calcInstanceMap.get(
                        assetMeasure.measurementId
                      );
                      if (!calcInstance) {
                        throw new ConflictException(
                          `Calculation Instance with ID ${assetMeasure.measurementId} not found`
                        );
                      }

                      const inputVars = calcInputMap.get(calcInstance.id) || [];

                      await this.calcEngineRepo.updateCalcInputs(
                        calcInstance.id,
                        {
                          customerId: customer,
                          ispersisted: calcMetricInstance.ispersisted,
                          iswriteback: calcMetricInstance.writeback ?? false,
                          outputMesurementId: assetMeasure.measurementId,
                          pollPeriod: calcMetricInstance.pollPeriod,
                          templateId: calcMetricInstance.calculation.id,
                          inputs: calcInputs.map((input) => {
                            const isNumber =
                              input.constantNumber !== null &&
                              input.constantNumber !== undefined &&
                              !isNaN(Number(input.constantNumber));

                            const matchingVariable = inputVars.find(
                              (iv) =>
                                iv.inputLabel.trim().toLowerCase() ===
                                input.inputLabel.trim().toLowerCase()
                            );

                            return {
                              inputLabel: input.inputLabel,
                              comment: input.comment,
                              inputId: matchingVariable?.id,
                              constantType: isNumber ? "number" : "string",
                              constantValue: input.constantNumber,
                              measurementId: input.metric?.id
                                ? affectedMeasurements.find(
                                    (m) => m.metricId === input.metric!.id
                                  )?.measurementId ?? null
                                : null,
                            };
                          }),
                        },
                        createdBy,
                        customer,
                        headers,
                        em
                      );
                    })
                );
              }
            }
          })
        );
      }
    });
  }
}
