import { Migration } from "@mikro-orm/migrations";

export class Migration20250630053927_rename_assettemplate_on_asset extends Migration {
  async up(): Promise<void> {
    // Rename the column to lowercase so PostgreSQL can use it without quotes
    this.addSql(
      'alter table "asset" rename column "assetTemplate" to "assettemplate";'
    );
  }

  async down(): Promise<void> {
    // Revert back to original name with PascalCase
    this.addSql(
      'alter table "asset" rename column "assettemplate" to "assetTemplate";'
    );
  }
}
