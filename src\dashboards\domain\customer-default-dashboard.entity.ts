import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ey,
  Property,
  ManyToOne,
  Ref,
  Unique,
} from "@mikro-orm/core";
import { Customer } from "src/customers/domain/customer.entity";
import { Dashboard } from "src/dashboards/domain/dashboard.entity";

@Entity()
@Unique({ properties: ["customer"] }) // Ensure only one default per customer
export class CustomerDefaultDashboard {
  @PrimaryKey()
  id!: number;

  @ManyToOne(() => Customer, {
    fieldName: "customer_id",
    nullable: false,
    hidden: true,
  })
  customer!: Ref<Customer>;

  @ManyToOne(() => Dashboard, {
    fieldName: "dashboard_id",
    nullable: false,
    hidden: true,
  })
  dashboard!: Ref<Dashboard>;

  @Property({ length: 6, hidden: true, nullable: true })
  createdAt?: Date = new Date();

  @Property({ length: 6, hidden: true, nullable: true })
  updatedAt?: Date;
}
