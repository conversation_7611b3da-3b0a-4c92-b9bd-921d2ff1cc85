import { MikroOrmModule } from "@mikro-orm/nestjs";
import { <PERSON><PERSON>, Module, forwardRef } from "@nestjs/common";
import { AssetModule } from "src/assets/asset.module";
import { Asset } from "src/assets/domain/asset.entity";
import { AuthModule } from "src/authentication/auth.module";
import { DashboardTemplate } from "src/dashboard-template/domain/dashboard-template.entity";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Measurement } from "src/measurements/domain/measurement.entity";
import { MeasurementModule } from "src/measurements/measurement.module";
import { DashboardService } from "./dashboard.service";
import { DashboardsApiController } from "./dashboards.controller";
import { Dashboard } from "./domain/dashboard.entity";
import { DashboardDefault } from "./domain/default-dashboard.entity";
import { DashboardFavorite } from "./domain/favorite-dashboard.entity";
import { DashboardRepository } from "./repository/dashboard.repository";
import { CustomerDefaultDashboard } from "./domain/customer-default-dashboard.entity";
import { CustomerFavoriteDashboard } from "./domain/customer-favorite-dashboard.entity";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      Dashboard,
      DashboardDefault,
      DashboardFavorite,
      CustomerDefaultDashboard,
      CustomerFavoriteDashboard,
      Asset,
      Measurement,
      AssetMeasurement,
      DashboardTemplate,
    ]),
    forwardRef(() => AuthModule),
    AssetModule,
    MeasurementModule,
  ],
  providers: [
    DashboardService,
    DashboardRepository,
    {
      provide: Logger,
      useValue: new Logger(DashboardService.name),
    },
  ],
  controllers: [DashboardsApiController],
  exports: [DashboardService, DashboardRepository],
})
export class DashboardsModule {}
