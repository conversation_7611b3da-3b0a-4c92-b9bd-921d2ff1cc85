import { Injectable } from "@nestjs/common";
import { UnitsGroupId } from "./domain/units-group.entity";
import { MeasurementTypeId } from "src/measurements/domain/measurement-type.entity";
import { UnitOfMeasure } from "src/measurements/domain/unit-of-measure.entity";
import { UnitsGroupUnitRepository } from "./repository/units-group-unit.repository";
import { UnitOfMeasureService } from "src/measurements/unit-of-measure.service";
import { UnitsGroupRepository } from "./repository/units-group.repository";
import { UnitsGroupUnit } from "./domain/units-group-unit.entity";

@Injectable()
export class UnitsGroupService {
  constructor(
    private readonly unitsGroupRepository: UnitsGroupRepository,
    private readonly unitsGroupUnitRepository: UnitsGroupUnitRepository,
    private readonly unitOfMeasureService: UnitOfMeasureService
  ) {}

  async findMeasurementTypeDefaultUnit(
    unitsGroupId: UnitsGroupId,
    measurementTypeId: MeasurementTypeId
  ): Promise<UnitOfMeasure | null> {
    const unitsGroupUnit =
      await this.unitsGroupUnitRepository.findMeasurementTypeDefaultUnit(
        unitsGroupId,
        measurementTypeId
      );

    return unitsGroupUnit === null
      ? null
      : await this.unitOfMeasureService.getById(
          unitsGroupUnit.unitOfMeasure.id
        );
  }

  async findUnitByIdAndMeasurementType(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId
  ): Promise<UnitOfMeasure | null> {
    const unitsGroupUnit =
      await this.unitsGroupUnitRepository.findUnitByIdAndMeasurementType(
        unitsGroupId,
        unitOfMeasureId,
        measurementTypeId
      );

    return unitsGroupUnit === null
      ? null
      : await this.unitOfMeasureService.getById(
          unitsGroupUnit.unitOfMeasure.id
        );
  }

  async getAll() {
    return await this.unitsGroupRepository.getAll();
  }

  async getGroupUnits(
    unitsGroupId: UnitsGroupId,
    filters: {
      measurementTypeId?: MeasurementTypeId;
      includeAllUnits?: boolean;
    } = {}
  ): Promise<Array<UnitOfMeasure & { isMeasurementTypeDefault: boolean }>> {
    if (filters.includeAllUnits && filters.measurementTypeId) {
      const allUOMs = await this.unitOfMeasureService.getAllByMeasurementType(
        filters.measurementTypeId
      );

      return allUOMs.map((uom) => ({
        ...uom,
        isMeasurementTypeDefault: false,
      }));
    }

    const groupUnits = await this.unitsGroupUnitRepository.getAllByGroupId(
      unitsGroupId,
      filters
    );

    const unitsOfMeasure = await this.unitOfMeasureService.getAllById(
      groupUnits.map((groupUnit) => groupUnit.unitOfMeasure.id)
    );

    const unitsOfMeasureMap = unitsOfMeasure.reduce(
      (prev, unitOfMeasure) => prev.set(unitOfMeasure.id, unitOfMeasure),
      new Map<number, UnitOfMeasure>()
    );

    const result = [];
    for (const groupUnit of groupUnits) {
      const unitOfMeasure = unitsOfMeasureMap.get(groupUnit.unitOfMeasure.id);
      if (!unitOfMeasure) {
        throw new Error("Error loading unit of measure");
      }
      result.push({
        ...unitOfMeasure,
        isMeasurementTypeDefault:
          groupUnit.unitsGroup.id.toString() === unitsGroupId.toString() &&
          groupUnit.is_default,
      });
    }

    return result?.find((unit) => unit.isMeasurementTypeDefault)
      ? result
      : groupUnits.map((groupUnit) => {
          const unitOfMeasure = unitsOfMeasureMap.get(
            groupUnit.unitOfMeasure.id
          );

          if (!unitOfMeasure) {
            throw new Error("Error loading unit of measure");
          }
          return {
            ...unitOfMeasure,
            isMeasurementTypeDefault: groupUnit.is_default,
          };
        });
  }

  async addUnitGroupUnit(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId,
    unitsofUnitId: string
  ) {
    return await this.unitsGroupUnitRepository.setDefaultUniGroupUnit({
      unitsGroupId,
      measurementTypeId,
      unitOfMeasureId,
      unitsofUnitId,
    });
  }

  async getUnitGroupsUnits() {
    return await this.unitsGroupUnitRepository.getUnitGroupsUnits();
  }
  async createUnitGroupUnit(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId,
    is_default: boolean
  ) {
    return await this.unitsGroupUnitRepository.createUnitGroupUnit(
      unitsGroupId,
      unitOfMeasureId,
      measurementTypeId,
      is_default
    );
  }
  async updateUnitGroupUnit(
    unitsGroupId: UnitsGroupId,
    unitOfMeasureId: number,
    measurementTypeId: MeasurementTypeId,
    unitsofUnitId: number,
    is_default: boolean
  ) {
    return await this.unitsGroupUnitRepository.updateUnitGroupUnit(
      unitsGroupId,
      unitOfMeasureId,
      measurementTypeId,
      unitsofUnitId,
      is_default
    );
  }

  async createUnitOfMeasure(
    name: string,
    measurementTypeId: MeasurementTypeId
  ) {
    return await this.unitsGroupUnitRepository.createUnitOfMeasure(
      name,
      measurementTypeId
    );
  }

  async updateUnitOfMeasure(
    name: string,
    measurementTypeId: MeasurementTypeId,
    unitOfMeasureId: number
  ) {
    return await this.unitsGroupUnitRepository.updateUnitOfMeasure(
      name,
      measurementTypeId,
      unitOfMeasureId
    );
  }
  async getUnitOfMeasures() {
    return await this.unitOfMeasureService.getUnitOfMeausres();
  }
}
