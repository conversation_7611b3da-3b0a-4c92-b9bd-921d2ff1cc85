import { Test, TestingModule } from "@nestjs/testing";
import { AlertStatsService } from "./alert-stats.service";
import { ExcursionController } from "./excursions.controller";
import authConfiguration from "src/authentication/auth.config"; // <-- add this import

describe("ExcursionController", () => {
  let controller: ExcursionController;
  let service: AlertStatsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExcursionController],
      providers: [
        {
          provide: AlertStatsService,
          useValue: {
            getRangeGroupedStats: jest.fn(),
          },
        },
        {
          provide: authConfiguration.KEY, // <-- use the actual config key
          useValue: {
            activeCustomerKeyId: "customerId",
          },
        },
      ],
    }).compile();

    controller = module.get<ExcursionController>(ExcursionController);
    service = module.get<AlertStatsService>(AlertStatsService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("getAlertExcursionByRange", () => {
    it("should call service with correct params and return result", async () => {
      const mockHeaders = { customerId: 42 };
      const mockResult = [{ id: 1 }];
      (service.getRangeGroupedStats as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.getAlertExcursionByRange(
        mockHeaders as any,
        100,
        200
      );

      expect(service.getRangeGroupedStats).toHaveBeenCalledWith(42, 100, 200);
      expect(result).toBe(mockResult);
    });

    it("should handle missing start/end params", async () => {
      const mockHeaders = { customerId: 99 };
      const mockResult = [];
      (service.getRangeGroupedStats as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.getAlertExcursionByRange(
        mockHeaders as any
      );

      expect(service.getRangeGroupedStats).toHaveBeenCalledWith(
        99,
        undefined,
        undefined
      );
      expect(result).toBe(mockResult);
    });
  });
});
