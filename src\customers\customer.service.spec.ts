import { FilterQuery, Reference } from "@mikro-orm/core";
import { getRepositoryToken } from "@mikro-orm/nestjs";
import { Test } from "@nestjs/testing";
import { CustomerService } from "./customer.service";
import { Customer } from "./domain/customer.entity";
import { CustomerDefaultDashboard } from "src/dashboards/domain/customer-default-dashboard.entity";
import { CustomerFavoriteDashboard } from "src/dashboards/domain/customer-favorite-dashboard.entity";

describe("CustomerService", () => {
  describe("create", () => {
    beforeEach(() => {
      Reference.createFromPK = jest.fn();
    });

    test("customer with unique name id should be created successfully", async () => {
      const { customerService, repositoryMock } = await createCustomerService();

      await customerService.create(
        {
          nameId: "apple",
          name: "Apple",
          address: "California",
        },
        1
      );

      const createdCustomer = repositoryMock.persistAndFlush.mock
        .calls[0][0] as Customer;
      expect(createdCustomer.name).toBe("Apple");
      expect(createdCustomer.enabled).toBeTruthy();
      expect(createdCustomer.address).toBe("California");
    });

    test("customer with existing name id should throw an exception", async () => {
      const { customerService, repositoryMock } = await createCustomerService();
      repositoryMock.count = () => 1;

      await expect(
        customerService.create(
          {
            nameId: "not_new",
            name: "NotNew",
            address: "California",
          },
          1
        )
      ).rejects.toThrow('Customer with name id "not_new" already exists');
    });

    test("customer with invalid name id should throw an exception", async () => {
      const { customerService } = await createCustomerService();

      await expect(
        customerService.create(
          {
            nameId: "NotNew",
            name: "NotNew",
            address: "California",
          },
          1
        )
      ).rejects.toThrow("Invalid customer name id");
    });
  });

  describe("get by name id", () => {
    beforeEach(() => {
      Reference.createFromPK = jest.fn();
    });

    test("given a name id, repository findOne should be called with the correct filter", async () => {
      const { customerService, repositoryMock } = await createCustomerService();

      await customerService.findByNameId("apple");

      const customerQuery = repositoryMock.findOne.mock
        .calls[0][0] as FilterQuery<Customer>;
      expect(customerQuery).toStrictEqual({ nameId: "apple" });
    });
  });

  describe("update - dashboard preferences", () => {
    beforeEach(() => {
      Reference.createFromPK = jest
        .fn()
        .mockImplementation((EntityClass, id) => {
          return {
            id,
            toJSON: () => ({ id }),
            unwrap: () => ({ id }),
            isInitialized: () => true,
            getEntity: () => ({ id }),
          } as any;
        });

      Reference.create = jest.fn().mockImplementation((entity) => {
        return {
          id: entity.id,
          toJSON: () => ({ id: entity.id }),
          unwrap: () => entity,
          isInitialized: () => true,
          getEntity: () => entity,
        } as any;
      });
    });

    test("sets default dashboard if not present", async () => {
      const { customerService, repositoryMock, defaultDashboardRepoMock } =
        await createCustomerService();

      const customer = { id: 10 } as Customer;
      repositoryMock.findOne.mockResolvedValue(customer);
      defaultDashboardRepoMock.findOne.mockResolvedValue(null);

      await customerService.update(10, { customerDefaultDashboardId: 99 }, 1);

      expect(defaultDashboardRepoMock.create).toHaveBeenCalled();
      expect(defaultDashboardRepoMock.persist).toHaveBeenCalled();
      expect(defaultDashboardRepoMock.flush).toHaveBeenCalled();
    });

    test("updates existing default dashboard", async () => {
      const { customerService, repositoryMock, defaultDashboardRepoMock } =
        await createCustomerService();

      const customer = { id: 10 } as Customer;
      const existingDefault = { dashboard: null };
      repositoryMock.findOne.mockResolvedValue(customer);
      defaultDashboardRepoMock.findOne.mockResolvedValue(existingDefault);

      await customerService.update(10, { customerDefaultDashboardId: 101 }, 1);

      expect(existingDefault.dashboard).toBeDefined();
      expect(defaultDashboardRepoMock.flush).toHaveBeenCalled();
    });

    test("sets favorite dashboards if provided", async () => {
      const { customerService, repositoryMock, favoriteDashboardRepoMock } =
        await createCustomerService();

      const customer = { id: 10 } as Customer;
      repositoryMock.findOne.mockResolvedValue(customer);

      await customerService.update(
        10,
        { customerFavoriteDashboardIds: [20, 21] },
        1
      );

      expect(favoriteDashboardRepoMock.nativeDelete).toHaveBeenCalledWith({
        customer: 10,
      });
      expect(favoriteDashboardRepoMock.create).toHaveBeenCalledTimes(2);
      expect(favoriteDashboardRepoMock.persistAndFlush).toHaveBeenCalled();
    });
  });
});

const createCustomerService = async () => {
  const repositoryMock = {
    count: () => 0,
    create: (id) => id,
    persistAndFlush: jest.fn(),
    findOne: jest.fn(),
  };

  const defaultDashboardRepoMock = {
    findOne: jest.fn(),
    create: jest.fn(),
    persist: jest.fn(),
    flush: jest.fn(),
  };

  const favoriteDashboardRepoMock = {
    nativeDelete: jest.fn(),
    create: jest.fn(),
    persistAndFlush: jest.fn(),
  };

  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(Customer),
        useValue: repositoryMock,
      },
      {
        provide: getRepositoryToken(CustomerDefaultDashboard),
        useValue: defaultDashboardRepoMock,
      },
      {
        provide: getRepositoryToken(CustomerFavoriteDashboard),
        useValue: favoriteDashboardRepoMock,
      },
      CustomerService,
    ],
  }).compile();

  const customerService = moduleRef.get(CustomerService);
  return {
    customerService,
    repositoryMock,
    defaultDashboardRepoMock,
    favoriteDashboardRepoMock,
  };
};
