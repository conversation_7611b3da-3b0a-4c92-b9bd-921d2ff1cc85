import {
  BadRequestException,
  Controller,
  Get,
  Inject,
  Query,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import authConfiguration from "src/authentication/auth.config";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { Role } from "src/authorization/domain/customer-user-role.entity";
import { HasRole } from "src/authorization/infra/roles.decorator";
import { AlertStatsService } from "./alert-stats.service";

@Controller({
  path: "excursions",
  version: "0",
})
export class ExcursionController {
  constructor(
    private readonly alertsStatsService: AlertStatsService,
    @Inject(authConfiguration.KEY)
    private readonly authConfig: ConfigType<typeof authConfiguration>
  ) {}

  @Get("/")
  async getAlertExcursion(
    @Query("customerId") customerId: number,
    @Query("start") startRaw: string,
    @Query("end") endRaw: string,
    @Query("interval") interval?: "daily" | "weekly" | "monthly",
    @Query("assetId") assetId?: number,
    @Query("measureId") measureId?: number,
    @Query("date") date?: string
  ) {
    const start = Number(startRaw);
    const end = Number(endRaw);

    if (!start || !end || isNaN(start) || isNaN(end)) {
      throw new BadRequestException("Invalid start or end timestamp.");
    }

    return await this.alertsStatsService.getGroupedStats(
      customerId,
      start,
      end,
      interval,
      assetId,
      measureId,
      date
    );
  }

  @Get("/range")
  @HasRole(Role.USER)
  async getAlertExcursionByRange(
    @CookieToken() headers: Request["headers"],
    @Query("start") start?: number,
    @Query("end") end?: number
  ) {
    const customerId: number = headers[this.authConfig.activeCustomerKeyId];
    return await this.alertsStatsService.getRangeGroupedStats(
      customerId,
      start,
      end
    );
  }
}
