import { MikroOrmModule } from "@mikro-orm/nestjs";
import { <PERSON><PERSON>, Module } from "@nestjs/common";
import { AssetModule } from "src/assets/asset.module";
import { AuthModule } from "src/authentication/auth.module";
import { CalcEngineModule } from "src/calc-engine/calc-engine.module";
import { CalculationInput } from "src/calc-engine/domain/calculation-input.entity";
import { CalculationInstance } from "src/calc-engine/domain/calculation-instance.entity";
import { CalculationTemplate } from "src/calc-engine/domain/calculation-template.entity";
import { CalculationMetricInput } from "src/calc-metrics-controller/domain/calculation-metric-input.entity";
import { CalculationMetricInstance } from "src/calc-metrics-controller/domain/calculation-metric-instance.entity";
import { CustomersModule } from "src/customers/customers.module";
import { DatabaseModule } from "src/db/database.module";
import { MeasurementModule } from "src/measurements/measurement.module";
import { AssetTemplateInstanceApiController } from "./asset-template-instance.controller";
import { AssetTemplateInstanceService } from "./asset-template-instance.service";
import { AssetTemplateApiController } from "./asset-template.controller";
import { AssetTemplateService } from "./asset-template.service";
import { AssetTemplateMeasurement } from "./domain/asset-template-measurement.entity";
import { AssetTemplate } from "./domain/asset-template.entity";
import { AssetTemplateInstanceMapper } from "./dto/asset-template-instance.mapper";
import { AssetTemplateMapper } from "./dto/asset-template.mapper";
import { AssetTemplateRepository } from "./repository/asset-template.repository";

@Module({
  imports: [
    MikroOrmModule.forFeature([
      AssetTemplate,
      AssetTemplateMeasurement,
      CalculationTemplate,
      CalculationMetricInput,
      CalculationMetricInstance,
      CalculationInstance,
      CalculationInput,
    ]),
    AuthModule,
    AssetModule,
    MeasurementModule,
    DatabaseModule,
    CustomersModule,
    CalcEngineModule,
  ],
  providers: [
    AssetTemplateMapper,
    AssetTemplateService,
    AssetTemplateRepository,
    AssetTemplateInstanceMapper,
    AssetTemplateInstanceService,
    {
      provide: Logger,
      useValue: new Logger(AssetTemplateService.name),
    },
  ],
  controllers: [AssetTemplateApiController, AssetTemplateInstanceApiController],
  exports: [],
})
export class AssetTemplateModule {}
