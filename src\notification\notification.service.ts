import { SendEmailCommand, SESClient } from "@aws-sdk/client-ses";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import AWS, { SES } from "aws-sdk";
import { <PERSON>wi<PERSON> } from "twilio";
@Injectable()
export class NotificationService {
  private aws: SES;
  private sesClient = new SESClient({ region: "us-east-1" });
  private client: Twilio;
  private twillioConfig: {
    send: boolean;
    account_sid: string;
    auth_token: string;
    email_api_token: string;
    email_from: string;
    sms_from_no: string;
    sendSMS: boolean;
  } = {
    send: false,
    account_sid: undefined,
    auth_token: undefined,
    email_api_token: undefined,
    email_from: undefined,
    sms_from_no: undefined,
    sendSMS: false,
  };
  public constructor(
    private configService: ConfigService,
    private logger: Logger
  ) {
    const notificationConfig = this.configService.get("notification");
    const SES_CONFIG = {
      accessKeyId: notificationConfig["access_key_id"],
      secretAccessKey: notificationConfig["secret_access_key"],
      region: notificationConfig["region"],
    };
    this.aws = new AWS.SES(SES_CONFIG);
    // Credentials are already set in the SESClient constructor; no need to assign here.
    this.sesClient = new SESClient({
      region: notificationConfig["region"],
      credentials: {
        accessKeyId: notificationConfig["access_key_id"],
        secretAccessKey: notificationConfig["secret_access_key"],
      },
    });
    this.twillioConfig = {
      send: notificationConfig.send,
      account_sid: notificationConfig.account_sid,
      auth_token: notificationConfig.auth_token,
      email_api_token: notificationConfig.email_api_token,
      email_from: notificationConfig.email_from,
      sms_from_no: notificationConfig.sms_from_no,
      sendSMS: notificationConfig.sendSMS,
    };
    this.client = new Twilio(
      this.twillioConfig.account_sid,
      this.twillioConfig.auth_token
    );
  }

  async sendSMS(to: string, message: string) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    this.logger.log("this.twillioConfig: ", this.twillioConfig.sendSMS);
    if (this.twillioConfig.send && this.twillioConfig.sendSMS) {
      try {
        this.logger.log("Sending SMS to: ", to, " with message: ", message);
        const msg = await this.client.messages.create({
          body: message || this.getSampleMessage(message),
          from: this.twillioConfig.sms_from_no,
          to: to,
        });
      } catch (e: any) {
        this.logger.error("error send sms", e.message);
        error.hasError = true;
        error.message = e.message;
      }
    } else {
      this.logger.warn("SMS sending is disabled in configuration.");
      error.hasError = true;
      error.message = "SMS sending is disabled in configuration.";
    }
    this.logger.log("SMS sent to: ", to, " with message: ", message);
    return error;
  }
  getSampleMessage(message: string): string {
    return `Limit Alert
    At: 2023-10-05T14:30:00 UTC
    Asset: AssetTag123
    Measurement: MeasurementTag456
    CurrentValue: 75.0
    Settings:
    Threshold: GE 70.0
    Return to Normal: LE 65.0`;
  }

  async sendEmail(
    to: string[],
    subject: string,
    body: string,
    htmlBody?: string
  ) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    if (this.twillioConfig.send) {
      try {
        const params = {
          Destination: {
            ToAddresses: to,
          },
          Message: {
            Body: {
              Html: {
                Charset: "UTF-8",
                Data: htmlBody ?? body ?? this.getSampleMessage(body),
              },
              Text: {
                Charset: "UTF-8",
                Data: body || this.getSampleMessage(body),
              },
            },
            Subject: {
              Charset: "UTF-8",
              Data: subject,
            },
          },
          Source: this.twillioConfig.email_from,
        };
        const command = new SendEmailCommand(params);
        await this.sesClient.send(command);
        // Removed commented-out logging statements to reduce clutter.
      } catch (e: any) {
        this.logger.error(`Error sending email : ${JSON.stringify(e)}`);
        error.hasError = true;
        error.message = e.message;
      }
    } else {
      this.logger.warn("Email sending is disabled in configuration.");
      error.hasError = true;
      error.message = "Email sending is disabled in configuration.";
    }
    this.logger.log(
      `Email sent to:  ${to} with subject:  ${subject}, and body: ${body}`
    );
    return error;
  }

  async sendForgotPasswordEmail(
    to: string[],
    subject: string,
    body: string,
    htmlBody: string
  ) {
    let error: {
      hasError: boolean;
      message: string | {} | any;
    } = {
      hasError: false,
      message: {},
    };
    if (this.twillioConfig.send) {
      const emailResult = await this.sendEmail(to, subject, body, htmlBody);
      if (emailResult.hasError) {
        this.logger.error(
          `Error sending email : ${JSON.stringify(emailResult)}`
        );
        error.hasError = true;
        error.message = emailResult.message;
      }
    } else {
      error.hasError = true;
      error.message = "Email sending is disabled in configuration.";
      this.logger.warn("Email sending is disabled in configuration.");
    }
    this.logger.log(
      "Email sent to: ",
      to,
      " with subject: ",
      subject,
      " and body: ",
      body
    );
    return error;
  }

  async connect() {
    this.logger.log("connected");
  }

  async close() {
    this.logger.log("closed");
  }
}
