version: '3.8'

services:
  postgres-performance-test:
    image: postgres:15
    container_name: brompton-performance-test-db
    environment:
      POSTGRES_DB: dataloggertest
      POSTGRES_USER: testuser
      POSTGRES_PASSWORD: testpass123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_performance_data:/var/lib/postgresql/data
      - ./performance-test-scripts:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c pg_stat_statements.max=10000
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=100
      -c work_mem=256MB
      -c maintenance_work_mem=512MB
      -c effective_cache_size=2GB
      -c random_page_cost=1.1
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d dataloggertest"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis-performance-test:
    image: redis:7-alpine
    container_name: brompton-performance-test-redis
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_performance_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

volumes:
  postgres_performance_data:
  redis_performance_data:
