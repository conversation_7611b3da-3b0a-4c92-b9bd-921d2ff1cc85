import { ConflictException, Injectable } from "@nestjs/common";
import { Dashboard } from "./domain/dashboard.entity";
import { DashboardFavorite } from "./domain/favorite-dashboard.entity";
import { DefaultDashboardType } from "./dto/dashboard.dto";
import { UpdateDashboardDto } from "./dto/update-dashboard.dto";
import { DashboardRepository } from "./repository/dashboard.repository";

/* Used to pass data to the service from controller */
export type DashboardCreationData = Omit<
  Dashboard,
  | "id"
  | "createdAt"
  | "updatedAt"
  | "createdBy"
  | "updatedBy"
  | "customer"
  | "deletedAt"
  | "deletedBy"
> & {
  asset_id?: number;
  dashboard_template_id?: number;
  favourite?: boolean;
};

export type DashboardUpdateData = DashboardCreationData & { id };

@Injectable()
export class DashboardService {
  constructor(private readonly dashboardsRepository: DashboardRepository) {}

  async create(
    dashboard: DashboardCreationData,
    userId: number,
    customerId: number
  ) {
    return this.dashboardsRepository.add(dashboard, userId, customerId);
  }

  async findById(id: number): Promise<Dashboard | null> {
    return this.dashboardsRepository.findById(id);
  }

  async findDefaultById(
    customerId,
    id: number,
    userId: number
  ): Promise<DefaultDashboardType | null> {
    return this.dashboardsRepository.findByIdWithDefault(
      customerId,
      id,
      userId
    );
  }

  async findAllByCustomerId(
    customerId: number,
    userId: number
  ): Promise<{
    items: Dashboard[];
    total: number;
    customerDefaultDashboardId: number | null;
    customerFavoriteDashboardIds: number[];
  }> {
    return this.dashboardsRepository.findAllByCustomerId(customerId, userId);
  }

  async update(
    dashboard: UpdateDashboardDto,
    userId: number,
    dashboardId: number,
    customerId: number
  ) {
    return this.dashboardsRepository.update(
      dashboard,
      userId,
      dashboardId,
      customerId
    );
  }

  async delete(dashboardId: number, userId: number) {
    return this.dashboardsRepository.delete(dashboardId, userId);
  }

  async defaultDashboard(
    dashboardId: number,
    userId: number,
    customerId: number,
    status: boolean
  ) {
    return this.dashboardsRepository.defaultDashboard(
      dashboardId,
      userId,
      customerId,
      status
    );
  }

  async favoriteDashboard(
    dashboardId: number,
    userId: number,
    customerId: number,
    status: boolean
  ): Promise<Number> {
    if (status) {
      const checkFavoriteDashboard: DashboardFavorite =
        await this.dashboardsRepository.findFavoriteDashboard(
          dashboardId,
          userId,
          customerId
        );

      if (
        checkFavoriteDashboard &&
        dashboardId === checkFavoriteDashboard.dashboard.id
      ) {
        throw new ConflictException(
          `Dashboard id ${dashboardId} is already favorite for this user.`
        );
      }

      return this.dashboardsRepository.createFavoriteDashboard(
        dashboardId,
        userId,
        customerId
      );
    } else {
      return this.dashboardsRepository.removeFavoriteDashboard(
        dashboardId,
        userId,
        customerId
      );
    }
  }

  async migrateDashboard(customerId: number, dashboardId: number) {
    return this.dashboardsRepository.migrateDashboard(customerId, dashboardId);
  }
  async syncDashboard(customerid: number, dashboardId: number) {
    return this.dashboardsRepository.autoSyncDashboard(customerid, dashboardId);
  }
}
