# Performance Testing Setup Guide

## Quick Start

1. **Start the test database:**
```bash
docker-compose -f docker-compose.performance-test.yml up -d
```

2. **Wait for database to be ready:**
```bash
docker-compose -f docker-compose.performance-test.yml logs -f postgres-performance-test
```

3. **Connect to test database:**
```bash
# Using psql
psql -h localhost -p 5433 -U testuser -d dataloggertest

# Or using your preferred database client
# Host: localhost
# Port: 5433
# Database: dataloggertest
# Username: testuser
# Password: testpass123
```

## Testing Procedure

### Phase 1: Baseline Performance (Current State)
```sql
-- Connect to database and run baseline tests
\i performance-test-scripts/04-test-queries.sql
```

### Phase 2: Add Performance Indexes
```sql
-- Add the recommended indexes
\i performance-test-scripts/03-performance-indexes.sql
```

### Phase 3: Compare Performance
```sql
-- Run the same test queries again to see improvement
\i performance-test-scripts/04-test-queries.sql
```

## Expected Results

### Before Optimization:
- Query time: 5-50+ seconds
- High buffer reads
- Sequential scans on large tables
- No index usage on excursion_stats

### After Optimization:
- Query time: <1 second
- Minimal buffer reads
- Index scans only
- 90%+ performance improvement

## Database Configuration

The test database is configured with:
- `pg_stat_statements` enabled for query analysis
- Optimized memory settings for performance testing
- Query logging enabled for debugging
- Statement timeout set to 30 seconds

## Sample Data

The test database contains:
- 2 customers
- 200 assets (100 per customer)
- 1,000 measurements
- 400 alerts (200 per customer)
- 50,000 excursion_stats records (last 90 days)

## Monitoring Queries

### Check index usage:
```sql
SELECT 
    schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes 
WHERE tablename = 'excursion_stats'
ORDER BY idx_scan DESC;
```

### Check query performance:
```sql
SELECT 
    query, calls, total_time, mean_time, rows
FROM pg_stat_statements 
WHERE query LIKE '%excursion_stats%'
ORDER BY total_time DESC;
```

### Check buffer cache hit ratio:
```sql
SELECT 
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%excursion_stats%';
```

## Cleanup

```bash
# Stop and remove containers
docker-compose -f docker-compose.performance-test.yml down

# Remove volumes (optional - removes all test data)
docker-compose -f docker-compose.performance-test.yml down -v
```

## Production Migration Notes

⚠️ **IMPORTANT**: Never run these commands against production databases!

When ready to apply to production:
1. Test all indexes on staging environment first
2. Use `CREATE INDEX CONCURRENTLY` to avoid blocking
3. Monitor query performance before and after
4. Have rollback plan ready
5. Apply during maintenance window if possible
