/**
 * Test script for the responsive layout migration
 * This script demonstrates how the migration transforms dashboard data
 */

// Sample dashboard data before migration
const sampleDashboardDataBefore = {
  widget: {
    widgets: [
      {
        type: "chart",
        id: "widget-1",
        settings: {
          title: { value: "Sample Chart", isVisible: true, color: "#000" },
          // ... other chart settings
        }
      },
      {
        type: "stats",
        id: "widget-2", 
        settings: {
          title: { value: "Sample Stats", isVisible: true, color: "#000" },
          // ... other stats settings
        }
      }
    ],
    widgetLayout: [
      { i: "widget-1", x: 0, y: 0, w: 6, h: 4 },
      { i: "widget-2", x: 6, y: 0, w: 6, h: 4 }
    ]
  },
  topPanel: {
    // ... top panel settings
  },
  chart: {
    // ... global chart settings
  }
};

// Function to simulate the migration transformation
function simulateMigration(dashboardData: any) {
  // Check if already migrated
  if (dashboardData.responsiveLayouts || dashboardData.desktopMobile !== undefined) {
    console.log("Dashboard already has responsive layouts, skipping...");
    return dashboardData;
  }

  // Ensure widget property exists
  if (!dashboardData.widget) {
    dashboardData.widget = {
      widgets: [],
      widgetLayout: []
    };
  }

  // Get current layout
  const currentWidgetLayout = dashboardData.widget.widgetLayout || [];

  // Add responsive layout structure
  dashboardData.desktopMobile = 0; // Default to desktop mode
  dashboardData.responsiveLayouts = {
    desktop: {
      widgetLayout: [...currentWidgetLayout] // Copy current layout as desktop
    },
    mobile: {
      widgetLayout: [...currentWidgetLayout] // Copy current layout as mobile (same initially)
    }
  };

  return dashboardData;
}

// Test the migration
console.log("=== BEFORE MIGRATION ===");
console.log(JSON.stringify(sampleDashboardDataBefore, null, 2));

const migratedData = simulateMigration(JSON.parse(JSON.stringify(sampleDashboardDataBefore)));

console.log("\n=== AFTER MIGRATION ===");
console.log(JSON.stringify(migratedData, null, 2));

console.log("\n=== VERIFICATION ===");
console.log("✓ Has desktopMobile property:", migratedData.desktopMobile !== undefined);
console.log("✓ Has responsiveLayouts property:", migratedData.responsiveLayouts !== undefined);
console.log("✓ Desktop layout matches original:", 
  JSON.stringify(migratedData.responsiveLayouts.desktop.widgetLayout) === 
  JSON.stringify(sampleDashboardDataBefore.widget.widgetLayout)
);
console.log("✓ Mobile layout matches original:", 
  JSON.stringify(migratedData.responsiveLayouts.mobile.widgetLayout) === 
  JSON.stringify(sampleDashboardDataBefore.widget.widgetLayout)
);
console.log("✓ Original widgetLayout preserved:", 
  JSON.stringify(migratedData.widget.widgetLayout) === 
  JSON.stringify(sampleDashboardDataBefore.widget.widgetLayout)
);

// Test rollback
function simulateRollback(dashboardData: any) {
  if (!dashboardData.responsiveLayouts && dashboardData.desktopMobile === undefined) {
    console.log("Dashboard doesn't have responsive layouts, skipping rollback...");
    return dashboardData;
  }

  // Remove responsive layout properties
  delete dashboardData.desktopMobile;
  delete dashboardData.responsiveLayouts;

  return dashboardData;
}

console.log("\n=== TESTING ROLLBACK ===");
const rolledBackData = simulateRollback(JSON.parse(JSON.stringify(migratedData)));
console.log("✓ Rollback successful:", 
  JSON.stringify(rolledBackData) === JSON.stringify(sampleDashboardDataBefore)
);
