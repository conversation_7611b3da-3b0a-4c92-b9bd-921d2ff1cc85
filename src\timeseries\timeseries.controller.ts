import { HttpService } from "@nestjs/axios";
import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  UseGuards,
  ValidationPipe,
} from "@nestjs/common";
import { ApiOkResponse } from "@nestjs/swagger";
import qs from "qs";
import { firstValueFrom } from "rxjs";
import { AuthUser } from "src/authentication/infra/auth-user.guard";
import { CookieToken } from "src/authentication/infra/cookieToken";
import { CsrfGuard } from "src/authentication/infra/csrf.guard";
import { JwtAuthGuard } from "src/authentication/infra/jwt-auth.guard";
import { User } from "src/users/domain/user.entity";
import {
  ForecastResponseDto,
  GetAggregationDto,
  GetCurrentTimeSeriesDto,
  GetForecastDto,
  GetHistoryTimeSeriesDto,
  GetLastReadingsDto,
  PostAllTimeSeriesDto,
  PostAllTimeSeriesResDto,
} from "./dto/timeseries.dto";
import { TIMESERIES_CONFIG, TimeSeriesConfig } from "./timeseries.config";

@Controller({ version: ["0"], path: "timeseries" })
@UseGuards(JwtAuthGuard, CsrfGuard)
export class TimeseriesApiController {
  private readonly timeSeriesApiBaseUrl;

  constructor(
    private readonly httpService: HttpService,
    @Inject(TIMESERIES_CONFIG)
    private readonly timeSeriesConfig: TimeSeriesConfig
  ) {
    const protocol = this.timeSeriesConfig.ssl ? "https" : "http";
    this.timeSeriesApiBaseUrl = `${protocol}://${this.timeSeriesConfig.host}api/${this.timeSeriesConfig.version}/timeseries`;
  }

  private checkLastSeriesStatus(timeseries) {
    const lastValue = timeseries[timeseries.length - 1] ?? 0;
    const secondLastValue = timeseries[timeseries.length - 2] ?? 0;

    if (secondLastValue && lastValue) {
      if (lastValue === secondLastValue) {
        return "same";
      } else {
        return lastValue > secondLastValue ? "up" : "down";
      }
    } else {
      return "same";
    }
  }

  @Get("dummy/:customerId")
  async getDummyData(
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"],
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetAggregationDto
  ) {
    return [
      {
        tag: query.meas_id,
        agg: query.agg,
        period: query.agg_period,
        asset_tz: {
          asset_tz: query.browser_tz,
          utc_offset: -25200000,
        },
        "ts,val": [
          [1727626648247, 61.14527601576667],
          [1727626708247, 61.06065183138334],
          [1727626768247, 61.103645843391675],
          [1727626828247, 61.13408275255],
          [1727626888247, 61.125039080183335],
          [1727626948247, 61.17069169895],
          [1727627008247, 61.18601981831667],
          [1727627068247, 61.26894198333334],
          [1727627128247, 61.29732924925],
          [1727627188247, 61.25327946846666],
          [1727627248247, 61.16722101636667],
          [1727627308247, 61.02589924835],
          [1727627368247, 60.935505202399995],
          [1727627428247, 60.87793372132499],
          [1727627488247, 60.86876843513333],
          [1727627548247, 60.977483533033336],
          [1727627608247, 61.02306700165],
          [1727627668247, 61.124086166366666],
          [1727627728247, 61.22790867672501],
          [1727627788247, 61.16650303138333],
          [1727627848247, 61.095355106008334],
          [1727627908247, 61.01277721816667],
          [1727627968247, 60.936505508258335],
          [1727628028247, 60.919793525524994],
        ],
        proc_time_sec: 0.061034247,
        tag_meta: {
          uom: "volts",
        },
      },
    ];
  }

  @Post("all/:customerId")
  @ApiOkResponse({ type: PostAllTimeSeriesResDto })
  @HttpCode(200)
  async getAll(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Body(ValidationPipe) body: PostAllTimeSeriesDto,
    @CookieToken() headers: Request["headers"]
  ) {
    const aggRequests = [];

    body.agg.map((item) => {
      aggRequests.push(
        this.httpProxy(`${this.timeSeriesApiBaseUrl}/agg/${customerId}`, item, {
          "be-csrftoken": headers["be-csrftoken"],
          Cookie: headers["cookie"],
        })
      );
    });

    const aggRes = await Promise.all(aggRequests);
    if (aggRes.length === 0) {
      return {
        agg: [],
      };
    }

    const aggData = [];
    aggRes?.map((item, index) => {
      const DailyTimeSeries = item?.length >= 1 ? item[0]["ts,val"] : [];
      if (DailyTimeSeries && DailyTimeSeries?.length >= 1) {
        aggData?.push({
          meas_id: body.agg[index].meas_id ?? "",
          timeseries: DailyTimeSeries[DailyTimeSeries?.length - 1] ?? [],
          status: this.checkLastSeriesStatus(DailyTimeSeries) ?? "",
          data: item ?? [],
        });
      } else {
        aggData?.push({
          meas_id: body.agg[index].meas_id ?? "",
          timeseries: [],
          status: "",
          data: [],
        });
      }
    });
    return {
      agg: aggData,
    };
  }

  @Get("history/:customerId")
  async getHistory(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetHistoryTimeSeriesDto,
    @CookieToken() headers: Request["headers"]
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    return await this.httpProxy(
      `${this.timeSeriesApiBaseUrl}/history/${customerId}`,
      {
        meas_id: query.meas_id,
        start: query.start,
        end: query.end,
        use_asset_tz: query.use_asset_tz,
        "x-usepersistedcalcs": query["x-usepersistedcalcs"],
        browser_tz: query.browser_tz,
      },
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );
  }

  @Get("lastreadings/:customerId")
  async getLastReadings(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetLastReadingsDto,
    @CookieToken() headers: Request["headers"]
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    return await this.httpProxy(
      `${this.timeSeriesApiBaseUrl}/lastreadings/${customerId}`,
      {
        meas_id: query.meas_id,
      },
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );
  }

  private async httpProxy(url: string, queryParams: any, headers?: any) {
    const { data } = await firstValueFrom(
      this.httpService.get(url, {
        params: queryParams,
        paramsSerializer: (params) =>
          qs.stringify(params, { arrayFormat: "repeat" }),
        headers: headers,
      })
    );
    return data;
  }

  @Get("current/:customerId")
  async getCurrent(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetCurrentTimeSeriesDto,
    @CookieToken() headers: Request["headers"]
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    return await this.httpProxy(
      `${this.timeSeriesApiBaseUrl}/current/${customerId}`,
      {
        meas_id: query.meas_id,
        "x-usepersistedcalcs": query["x-usepersistedcalcs"],
        browser_tz: query.browser_tz,
      },
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );
  }

  @Get("agg/:customerId")
  async getAggregation(
    @AuthUser() authUser: User,
    @CookieToken() headers: Request["headers"],
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetAggregationDto
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    return await this.httpProxy(
      `${this.timeSeriesApiBaseUrl}/agg/${customerId}`,
      {
        meas_id: query.meas_id,
        start: query.start,
        end: query.end,
        agg: query.agg,
        agg_period: query.agg_period,
        use_asset_tz: query.use_asset_tz,
        groupby: query.groupby,
        "x-usepersistedcalcs": query["x-usepersistedcalcs"],
        browser_tz: query.browser_tz,
      },
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );
  }

  @Get("forecast/:customerId")
  @ApiOkResponse({ type: ForecastResponseDto })
  async getForecast(
    @AuthUser() authUser: User,
    @Param("customerId") customerId: string,
    @Query(ValidationPipe) query: GetForecastDto,
    @CookieToken() headers: Request["headers"]
  ) {
    if (!authUser.hasCustomerScope(Number(customerId))) {
      throw new ForbiddenException();
    }

    const forecastRes = await this.httpProxy(
      `${this.timeSeriesApiBaseUrl}/forecast/${customerId}`,
      {
        meas_id: query.meas_id,
        forecast: query.forecast,
        use_asset_tz: query.use_asset_tz,
        browser_tz: query.browser_tz,
        agg: query.agg,
      },
      {
        "be-csrftoken": headers["be-csrftoken"],
        Cookie: headers["cookie"],
      }
    );

    if (!forecastRes) {
      throw new InternalServerErrorException("Forecast fetch issue");
    }

    return forecastRes;
  }
}
