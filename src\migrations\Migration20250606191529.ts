import { Migration } from '@mikro-orm/migrations';

export class Migration20250606191529 extends Migration {

  async up(): Promise<void> {
    this.addSql('create table "customer_favorite_dashboard" ("id" serial primary key, "customer_id" int not null, "dashboard_id" int not null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null);');

    this.addSql('create table "customer_default_dashboard" ("id" serial primary key, "customer_id" int not null, "dashboard_id" int not null, "created_at" timestamptz(6) null, "updated_at" timestamptz(6) null);');
    this.addSql('alter table "customer_default_dashboard" add constraint "customer_default_dashboard_customer_id_unique" unique ("customer_id");');

    this.addSql('alter table "customer_favorite_dashboard" add constraint "customer_favorite_dashboard_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade;');
    this.addSql('alter table "customer_favorite_dashboard" add constraint "customer_favorite_dashboard_dashboard_id_foreign" foreign key ("dashboard_id") references "dashboard" ("id") on update cascade;');

    this.addSql('alter table "customer_default_dashboard" add constraint "customer_default_dashboard_customer_id_foreign" foreign key ("customer_id") references "customer" ("id") on update cascade;');
    this.addSql('alter table "customer_default_dashboard" add constraint "customer_default_dashboard_dashboard_id_foreign" foreign key ("dashboard_id") references "dashboard" ("id") on update cascade;');

    this.addSql('alter table "dashboard_template" alter column "data" type text using ("data"::text);');
  }

  async down(): Promise<void> {
    this.addSql('drop table if exists "customer_favorite_dashboard" cascade;');

    this.addSql('drop table if exists "customer_default_dashboard" cascade;');

    this.addSql('alter table "dashboard_template" alter column "data" type varchar(255) using ("data"::varchar(255));');
  }

}
