import { EntityManager } from "@mikro-orm/core";
import { UserId } from "src/users/domain/user.entity";
import { AssetId } from "../asset.entity";

export const ASSET_BATCH_REMOVE_EVENT_KEY = "asset.batch-remove";
export class AssetBatchRemoveEvent {
  constructor(
    readonly assetIds: AssetId[],
    readonly occuredAt: Date,
    readonly runById: UserId,
    readonly headers: Request["headers"] | undefined,
    readonly emManager?: EntityManager
  ) {}

  readonly key = ASSET_BATCH_REMOVE_EVENT_KEY;
}
