import { Migration } from "@mikro-orm/migrations";

export class Migration20250605163019 extends Migration {
  async up(): Promise<void> {
    // Get all dashboard template records
    const dashboardTemplates = await this.execute(
      "SELECT id, data FROM \"dashboard_template\" WHERE data IS NOT NULL AND data != ''"
    );

    console.log(
      `Processing ${dashboardTemplates.length} dashboard template records for responsive layout migration...`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const dashboardTemplate of dashboardTemplates) {
      try {
        // Parse the existing JSON data
        const dashboardTemplateData = JSON.parse(dashboardTemplate.data);

        // Check if this dashboard template already has responsive layouts (skip if already migrated)
        if (
          dashboardTemplateData.responsiveLayouts ||
          dashboardTemplateData.desktopMobile !== undefined
        ) {
          console.log(
            `Dashboard template ${dashboardTemplate.id} already has responsive layouts, skipping...`
          );
          continue;
        }

        // Ensure the widget property exists
        if (!dashboardTemplateData.widget) {
          dashboardTemplateData.widget = {
            widgets: [],
            widgetLayout: [],
          };
        }

        // Get the current widgetLayout (fallback to empty array if not exists)
        const currentWidgetLayout =
          dashboardTemplateData.widget.widgetLayout || [];

        // Add the new responsive layout structure
        dashboardTemplateData.desktopMobile = 0; // Default to desktop mode
        dashboardTemplateData.responsiveLayouts = {
          desktop: {
            widgetLayout: [...currentWidgetLayout], // Copy current layout as desktop layout
            widgets: [...(dashboardTemplateData.widget.widgets || [])],
          },
          mobile: {
            widgetLayout: [...currentWidgetLayout], // Copy current layout as mobile layout (same initially)
            widgets: [...(dashboardTemplateData.widget.widgets || [])],
          },
        };

        // Keep the existing widgetLayout for backward compatibility
        // dashboardTemplateData.widget.widgetLayout remains unchanged

        // Convert back to JSON string
        const updatedData = JSON.stringify(dashboardTemplateData);

        // Update the dashboard template record
        await this.execute(
          'UPDATE "dashboard_template" SET data = ? WHERE id = ?',
          [updatedData, dashboardTemplate.id]
        );

        successCount++;
        console.log(
          `✓ Successfully migrated dashboard template ${dashboardTemplate.id}`
        );
      } catch (error) {
        errorCount++;
        console.error(
          `✗ Error migrating dashboard template ${dashboardTemplate.id}:`,
          error
        );

        // Log the problematic data for debugging (first 200 chars)
        const dataPreview = dashboardTemplate.data
          ? dashboardTemplate.data.substring(0, 200) + "..."
          : "null";
        console.error(`  Data preview: ${dataPreview}`);
      }
    }

    console.log(
      `Dashboard template migration completed: ${successCount} successful, ${errorCount} errors`
    );

    if (errorCount > 0) {
      console.warn(
        `Warning: ${errorCount} dashboard templates failed to migrate. Please check the logs above.`
      );
    }
  }

  async down(): Promise<void> {
    // Get all dashboard template records
    const dashboardTemplates = await this.execute(
      "SELECT id, data FROM \"dashboard_template\" WHERE data IS NOT NULL AND data != ''"
    );

    console.log(
      `Reverting responsive layout migration for ${dashboardTemplates.length} dashboard template records...`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const dashboardTemplate of dashboardTemplates) {
      try {
        // Parse the existing JSON data
        const dashboardTemplateData = JSON.parse(dashboardTemplate.data);

        // Check if this dashboard template has responsive layouts to remove
        if (
          !dashboardTemplateData.responsiveLayouts &&
          dashboardTemplateData.desktopMobile === undefined
        ) {
          console.log(
            `Dashboard template ${dashboardTemplate.id} doesn't have responsive layouts, skipping...`
          );
          continue;
        }

        // Remove the responsive layout properties
        delete dashboardTemplateData.desktopMobile;
        delete dashboardTemplateData.responsiveLayouts;

        // The original widgetLayout should still be intact, so no need to restore it

        // Convert back to JSON string
        const updatedData = JSON.stringify(dashboardTemplateData);

        // Update the dashboard template record
        await this.execute(
          'UPDATE "dashboard_template" SET data = ? WHERE id = ?',
          [updatedData, dashboardTemplate.id]
        );

        successCount++;
        console.log(
          `✓ Successfully reverted dashboard template ${dashboardTemplate.id}`
        );
      } catch (error) {
        errorCount++;
        console.error(
          `✗ Error reverting dashboard template ${dashboardTemplate.id}:`,
          error
        );
      }
    }

    console.log(
      `Dashboard template migration rollback completed: ${successCount} successful, ${errorCount} errors`
    );
  }
}
