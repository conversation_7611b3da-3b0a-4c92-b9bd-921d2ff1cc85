import { AlertStatsService } from "./alert-stats.service";

describe("AlertStatsService", () => {
  let service: AlertStatsService;
  let repo: any;

  beforeEach(async () => {
    repo = {
      getRangeGroupedStats: jest.fn(),
    };
    service = new AlertStatsService(repo as any);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("getRangeGroupedStats", () => {
    it("should call repository with correct params and return result", async () => {
      const mockResult = [{ id: 1 }];
      repo.getRangeGroupedStats.mockResolvedValue(mockResult);

      const result = await service.getRangeGroupedStats(5, 10, 20);

      expect(repo.getRangeGroupedStats).toHaveBeenCalledWith(5, 10, 20);
      expect(result).toBe(mockResult);
    });

    it("should handle missing start/end params", async () => {
      const mockResult = [];
      repo.getRangeGroupedStats.mockResolvedValue(mockResult);

      const result = await service.getRangeGroupedStats(7);

      expect(repo.getRangeGroupedStats).toHaveBeenCalledWith(
        7,
        undefined,
        undefined
      );
      expect(result).toBe(mockResult);
    });
    it("showing path to asset type entity for parent", async () => {
      const mockResult = [];
      repo.getRangeGroupedStats.mockResolvedValue(mockResult);

      const result = await service.getRangeGroupedStats(7, 10, 20);

      expect(repo.getRangeGroupedStats).toHaveBeenCalledWith(7, 10, 20);
      expect(result).toBe(mockResult);
    });
  });
});
