import { Reference } from "@mikro-orm/core";
import { AssetMeasurementRepository } from "./asset-measurement.repository";
import { EntityRepository, EntityManager } from "@mikro-orm/core";
import { SqlEntityManager } from "@mikro-orm/postgresql";
import { TransactionFactory } from "src/db/TransactionFactory";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { AssetMeasurement } from "../domain/asset-measurement.entity";
import { UserId } from "src/users/domain/user.entity";
import { of } from "rxjs";

jest.mock("@nestjs/axios");
jest.mock("@nestjs/config");

describe("AssetMeasurementRepository", () => {
  beforeAll(() => {
    jest.spyOn(Reference, "createFromPK").mockImplementation(
      () =>
        ({
          entity: {},
          load: jest.fn(),
          set: jest.fn(),
          unwrap: jest.fn(),
          isInitialized: true,
          isReference: true,
          getEntity: jest.fn(),
          getProperty: jest.fn(),
          getPrimaryKey: jest.fn(),
          toJSON: jest.fn(),
          toObject: jest.fn(),
        } as unknown as Reference<object>)
    );
  });
  let repo: AssetMeasurementRepository;
  let entityRepository: jest.Mocked<EntityRepository<AssetMeasurement>>;
  let entityManager: jest.Mocked<SqlEntityManager>;
  let transactionFactory: jest.Mocked<TransactionFactory>;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(() => {
    entityRepository = {
      persistAndFlush: jest.fn(),
      find: jest.fn(),
      count: jest.fn(),
      findOne: jest.fn(),
    } as any;

    // Mock SqlEntityManager with required methods
    entityManager = {
      findOne: jest.fn(),
      count: jest.fn(),
      find: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        having: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue(undefined),
        getKnexQuery: jest.fn().mockReturnValue([]),
      }),
      // Add any other methods used in your repository
    } as unknown as jest.Mocked<SqlEntityManager>;

    transactionFactory = {
      run: jest.fn((fn) => fn(entityManager)),
    } as any;
    httpService = {
      delete: jest.fn(),
    } as any;
    configService = {
      get: jest.fn().mockReturnValue("http://mock-alert-url/"),
    } as any;

    repo = new AssetMeasurementRepository(
      entityRepository,
      entityManager,
      transactionFactory,
      httpService,
      configService
    );
    repo["alertScheduleURL"] = "http://mock-alert-url/";
  });

  it("should add asset measurement", async () => {
    const assetMeasurement = { measurement: {} } as any;
    await repo.add(assetMeasurement, 1 as UserId);
    expect(entityRepository.persistAndFlush).toHaveBeenCalledWith(
      assetMeasurement
    );
  });

  it("should get all by asset id", async () => {
    entityRepository.find.mockResolvedValue([
      { datasourceId: 1 },
      { datasourceId: 2 },
    ] as any);
    const result = await repo.getAllByAssetId(1);
    expect(result).toEqual([{ datasourceId: 1 }]);
  });

  it("should count by asset id", async () => {
    entityRepository.count.mockResolvedValue(5);
    const count = await repo.countByAssetId(1, {});
    expect(count).toBe(5);
  });

  it("should find by id", async () => {
    entityRepository.findOne.mockResolvedValue({ id: 1 } as any);
    const result = await repo.findById(1, {});
    expect(result).toEqual({ id: 1 });
  });

  it("should remove asset measurement", async () => {
    const assetMeasurement = { measurementId: 1, measurement: {} } as any;
    entityManager.count.mockResolvedValue(1);
    entityRepository.persistAndFlush.mockResolvedValue(undefined);
    httpService.delete.mockReturnValue(of({ data: {} }) as any);

    // Cast headers to any to avoid TS error
    await repo.remove(
      assetMeasurement,
      { "be-csrftoken": "token", cookie: "cookie" } as any,
      1 as UserId,
      entityManager
    );
    expect(entityRepository.persistAndFlush).toHaveBeenCalled();
    expect(httpService.delete).toHaveBeenCalled();
  });

  // Add more tests for batchRemoveByAssetId, update, etc.
  it("batchRemoveByAssetId should call entityManager.createQueryBuilder and chain methods", async () => {
    const assetIds = [1];
    const occuredAt = new Date();
    const deletedById = 123 as UserId;
    const headers = { "be-csrftoken": "token", cookie: "cookie" } as any;
    // Create mocks for the query builder chain
    const selectMock = jest.fn().mockReturnThis();
    const whereMock = jest.fn().mockReturnThis();
    const updateMock = jest.fn().mockReturnThis();
    const executeMock = jest.fn().mockResolvedValue(undefined);
    const getResultListMock = jest
      .fn()
      .mockResolvedValueOnce([]) // for first call (e.g., linkedMeasurementIds)
      .mockResolvedValueOnce([]) // for second call (e.g., measurementIdsToDelete)
      .mockResolvedValueOnce([]); // for dashboards (should be an array)
    const getKnexQueryMock = jest.fn().mockReturnValue([]);
    const groupByMock = jest.fn().mockReturnThis();
    const havingMock = jest.fn().mockReturnThis();
    const qbMock = {
      select: selectMock,
      where: whereMock,
      groupBy: groupByMock,
      having: havingMock,
      update: updateMock,
      execute: executeMock,
      getResultList: getResultListMock,
      getKnexQuery: getKnexQueryMock,
    };
    (entityManager.createQueryBuilder as jest.Mock).mockReturnValue(qbMock);
    await repo.batchRemoveByAssetId(
      assetIds,
      occuredAt,
      deletedById,
      headers,
      entityManager
    );
    expect(entityManager.createQueryBuilder).toHaveBeenCalledWith(
      AssetMeasurement
    );
    expect(whereMock).toHaveBeenCalled();
    expect(updateMock).toHaveBeenCalled();
    expect(executeMock).toHaveBeenCalled();
  });

  it("should update asset measurement", async () => {
    const assetMeasurement = { id: 1, measurement: {} } as any;
    entityRepository.persistAndFlush.mockResolvedValue(undefined);
    await repo.update(assetMeasurement, 1 as UserId);
    expect(entityRepository.persistAndFlush).toHaveBeenCalledWith(
      assetMeasurement
    );
  });

  it("should handle remove with no linked measurements", async () => {
    const assetMeasurement = { measurementId: 1, measurement: {} } as any;
    entityManager.count.mockResolvedValue(0);
    entityRepository.persistAndFlush.mockResolvedValue(undefined);
    httpService.delete.mockReturnValue(of({ data: {} }) as any);

    await repo.remove(
      assetMeasurement,
      { "be-csrftoken": "token", cookie: "cookie" } as any,
      1 as UserId,
      entityManager
    );
    expect(entityRepository.persistAndFlush).toHaveBeenCalled();
    expect(httpService.delete).toHaveBeenCalled();
  });

  it("should handle remove with linked measurements", async () => {
    const assetMeasurement = { measurementId: 1, measurement: {} } as any;
    entityManager.count.mockResolvedValue(1);
    entityRepository.persistAndFlush.mockResolvedValue(undefined);
    httpService.delete.mockReturnValue(of({ data: {} }) as any);

    await repo.remove(
      assetMeasurement,
      { "be-csrftoken": "token", cookie: "cookie" } as any,
      1 as UserId,
      entityManager
    );
    expect(entityRepository.persistAndFlush).toHaveBeenCalled();
    expect(httpService.delete).toHaveBeenCalled();
  });

  it("should handle batchRemoveByAssetId with no linked measurements", async () => {
    const assetIds = [1];
    const occuredAt = new Date();
    const deletedById = 123 as UserId;
    const headers = { "be-csrftoken": "token", cookie: "cookie" } as any;

    entityManager.createQueryBuilder.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      having: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue(undefined),
      getResultList: jest.fn().mockResolvedValue([]),
      getKnexQuery: jest.fn().mockReturnValue([]),
    } as any);

    await repo.batchRemoveByAssetId(
      assetIds,
      occuredAt,
      deletedById,
      headers,
      entityManager
    );
    expect(entityManager.createQueryBuilder).toHaveBeenCalledWith(
      AssetMeasurement
    );
  });

  it("should handle batchRemoveByAssetId with linked measurements", async () => {
    const assetIds = [1];
    const occuredAt = new Date();
    const deletedById = 123 as UserId;
    const headers = { "be-csrftoken": "token", cookie: "cookie" } as any;

    entityManager.createQueryBuilder.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      having: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue(undefined),
      getResultList: jest.fn().mockResolvedValue([]),
      getKnexQuery: jest.fn().mockReturnValue([]),
    } as any);

    await repo.batchRemoveByAssetId(
      assetIds,
      occuredAt,
      deletedById,
      headers,
      entityManager
    );
    expect(entityManager.createQueryBuilder).toHaveBeenCalledWith(
      AssetMeasurement
    );
  });
});
