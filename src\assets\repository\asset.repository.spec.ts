import { Test } from "@nestjs/testing";
import { Asset } from "../domain/asset.entity";
import { AssetRepository } from "./asset.repository";
// import { AssetMeasurement } from "../domain/asset-measurement.entity";
import { getRepositoryToken } from "@mikro-orm/nestjs";
import { EntityManager, EntityRepository } from "@mikro-orm/postgresql";
import { AssetMeasurement } from "src/measurements/domain/asset-measurement.entity";
import { Alerts } from "../../alert/domain/alert.entity";
import { Dashboard } from "../../dashboards/domain/dashboard.entity";
import { HierarchyGraphService } from "../hierarchy-graph.service";

describe("AssetRepository", () => {
  describe("findById", () => {
    test("querying an asset from a customer should set corresponding filter", async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      await assetRepository.findById(43, { customerId: 84 });

      const filterCondition = entityRepositoryMock.findOne.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        id: 43,
        customer: 84,
        deletedAt: null,
      });
    });
  });

  describe("getAllByCustomerId", () => {
    test("customer id should be set in find query", async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      await assetRepository.getAllByCustomerId(42);

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
        ],
      });
    });

    test("given ids, customer id and ids should be set in find query", async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { ids: [4, 5, 6] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          { id: { $in: [4, 5, 6] } },
        ],
      });
    });

    test("given parentIds filter with -1, customer id and parent filter should be set in find query", async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { parentIds: [-1] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          {
            $or: [
              { parentHierarchies: { parent: { $eq: null }, deletedAt: null } },
              { parentHierarchies: { parent: { $in: [-1] }, deletedAt: null } },
            ],
          },
        ],
      });
    });

    test("given parentIds filter without -1, customer id and parent filter should be set in find query", async () => {
      const { assetRepository, entityRepositoryMock } =
        await createAssetRepository();

      assetRepository.getAllByCustomerId(42, { parentIds: [5, 665] });

      const filterCondition = entityRepositoryMock.find.mock.calls[0][0];
      expect(filterCondition).toStrictEqual({
        $and: [
          { customer: 42, deletedAt: null, parentHierarchies: { $ne: null } },
          { parentHierarchies: { parent: { $in: [5, 665] }, deletedAt: null } },
        ],
      });
    });
  });

  describe("removeHierarchy", () => {
    test("should update AssetHierarchy and Asset as deleted and return nodeIds", async () => {
      const emMock = {
        createQueryBuilder: jest.fn().mockReturnValue({
          update: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          execute: jest.fn().mockResolvedValue(undefined),
        }),
        execute: jest.fn().mockResolvedValue([
          { id: 1, parent: null, child: 2 },
          { id: 2, parent: 1, child: 3 },
        ]),
      };
      const hierarchyGraphServiceMock = {
        mapDbRowsToHierarchy: jest.fn().mockReturnValue({
          parentChildrenMap: { 1: [2], 2: [3] },
          childParentsMap: { 2: [1], 3: [2] },
        }),
        findCascadeRemoveIds: jest
          .fn()
          .mockReturnValue({ linkIds: [1, 2], nodeIds: [1, 2, 3] }),
      };
      const assetRepository = new AssetRepository(
        {
          findOne: jest.fn(),
          find: jest.fn(),
          persistAndFlush: jest.fn(),
        } as any,
        emMock as any,
        hierarchyGraphServiceMock as any,
        {} as any,
        {} as any,
        {} as any
      );
      // Mock getAssetHierarchyQueryBuilder to return a dummy query object
      assetRepository["getAssetHierarchyQueryBuilder"] = jest
        .fn()
        .mockReturnValue("dummy-query");
      const rootAsset = { id: 1 } as any;
      const runById = 99;
      const result = await assetRepository.removeHierarchy(rootAsset, runById);
      expect(emMock.createQueryBuilder).toHaveBeenCalledWith(
        expect.any(Function)
      );
      expect(hierarchyGraphServiceMock.mapDbRowsToHierarchy).toHaveBeenCalled();
      expect(
        hierarchyGraphServiceMock.findCascadeRemoveIds
      ).toHaveBeenCalledWith(1, { 1: [2], 2: [3] }, { 2: [1], 3: [2] });
      expect(result).toEqual([1, 2, 3]);
    });
  });

  describe("countHierarchyNodes", () => {
    test("should return the count of nodes in the hierarchy", async () => {
      const emMock = {
        createQueryBuilder: jest
          .fn()
          .mockReturnValue({ getKnex: jest.fn().mockReturnValue("qb") }),
        execute: jest.fn().mockResolvedValue([{}, {}, {}]),
      };
      const assetRepository = new AssetRepository(
        {
          findOne: jest.fn(),
          find: jest.fn(),
          persistAndFlush: jest.fn(),
        } as any,
        emMock as any,
        {
          mapDbRowsToHierarchy: jest.fn(),
          findCascadeRemoveIds: jest.fn(),
        } as any,
        {} as any,
        {} as any,
        {} as any
      );
      // Mock getAssetHierarchyQueryBuilder to return a query object with whereIn
      const qbMock = {
        whereIn: jest.fn().mockReturnThis(),
      };
      assetRepository["getAssetHierarchyQueryBuilder"] = jest
        .fn()
        .mockReturnValue(qbMock);
      const count = await assetRepository.countHierarchyNodes(1, [1, 2, 3]);
      expect(count).toBe(3);
      expect(qbMock.whereIn).toHaveBeenCalledWith("rec_ah.child", [1, 2, 3]);
      expect(emMock.execute).toHaveBeenCalledWith(qbMock);
    });
  });

  describe("findRelatedEntitiesToAsset", () => {
    test("should return related assets, measurements, alerts, and dashboards", async () => {
      const emMock = {
        find: jest
          .fn()
          .mockResolvedValueOnce([
            {
              id: 1,
              tag: "A",
              latitude: 0,
              longitude: 0,
              description: "desc",
              parentIds: [],
              childrenIds: [],
            },
            {
              id: 2,
              tag: "B",
              latitude: 1,
              longitude: 1,
              description: "desc2",
              parentIds: [],
              childrenIds: [],
            },
          ])
          .mockResolvedValueOnce([
            {
              id: 10,
              asset: { id: 1 },
              measurementId: 100,
              tag: "m1",
              datasourceId: 1,
              measurement: { enabled: true },
            },
            {
              id: 11,
              asset: { id: 2 },
              measurementId: 101,
              tag: "m2",
              datasourceId: 1,
              measurement: { enabled: null },
            },
            {
              id: 12,
              asset: { id: 2 },
              measurementId: 102,
              tag: "m3",
              datasourceId: 2,
              measurement: { enabled: true },
            }, // should be filtered out
          ]),
      };
      const alertsRepositoryMock = {
        find: jest.fn().mockResolvedValue([
          {
            id: 20,
            asset: { id: 1 },
            description: "alert1",
            measurement: { id: 100 },
          },
          {
            id: 21,
            asset: { id: 2 },
            description: "alert2",
            measurement: { id: 101 },
          },
        ]),
      };
      const dashboardRepositoryMock = {
        find: jest.fn().mockResolvedValue([
          { id: 30, asset: { id: 1 }, title: "dash1" },
          { id: 31, asset: { id: 2 }, title: "dash2" },
        ]),
      };
      const hierarchyGraphServiceMock = {
        mapDbRowsToHierarchy: jest.fn().mockReturnValue({
          parentChildrenMap: {},
          childParentsMap: {},
        }),
        findCascadeRemoveIds: jest.fn().mockReturnValue({ nodeIds: [1, 2] }),
      };
      const assetRepository = new AssetRepository(
        {
          findOne: jest.fn(),
          find: jest.fn(),
          persistAndFlush: jest.fn(),
        } as any,
        {
          execute: jest.fn().mockResolvedValue([]),
          find: emMock.find,
        } as any,
        hierarchyGraphServiceMock as any,
        { find: emMock.find } as any,
        alertsRepositoryMock as any,
        dashboardRepositoryMock as any
      );
      // Mock getAssetHierarchyQueryBuilder to return a query object
      assetRepository["getAssetHierarchyQueryBuilder"] = jest
        .fn()
        .mockReturnValue("qb");
      const result = await assetRepository.findRelatedEntitiesToAsset(1, {
        customerId: 42,
      });
      expect(result.assets.length).toBe(2);
      expect(result.assets[0].measurements.length).toBe(1);
      expect(result.assets[1].measurements.length).toBe(1);
      expect(result.assets[0].alerts.length).toBe(1);
      expect(result.assets[1].alerts.length).toBe(1);
      expect(result.assets[0].dashboards.length).toBe(1);
      expect(result.assets[1].dashboards.length).toBe(1);
    });
  });
});

const createAssetRepository = async () => {
  const entityRepositoryMock: jest.Mocked<
    Pick<EntityRepository<Asset>, "findOne" | "find">
  > = {
    findOne: jest.fn(),
    find: jest.fn(),
  };
  const assetMeasurementRepositoryMock = {
    find: jest.fn(),
    findOne: jest.fn(),
    persistAndFlush: jest.fn(),
  };
  const alertsRepositoryMock = {
    find: jest.fn(),
    findOne: jest.fn(),
    persistAndFlush: jest.fn(),
  };
  const dashboardRepositoryMock = {
    find: jest.fn(),
    findOne: jest.fn(),
    persistAndFlush: jest.fn(),
  };
  const moduleRef = await Test.createTestingModule({
    providers: [
      {
        provide: getRepositoryToken(Asset),
        useValue: entityRepositoryMock,
      },
      {
        provide: getRepositoryToken(AssetMeasurement),
        useValue: assetMeasurementRepositoryMock,
      },
      {
        provide: getRepositoryToken(Alerts),
        useValue: alertsRepositoryMock,
      },
      {
        provide: getRepositoryToken(Dashboard),
        useValue: dashboardRepositoryMock,
      },
      { provide: EntityManager, useValue: jest.fn() },
      { provide: HierarchyGraphService, useValue: new HierarchyGraphService() },
      AssetRepository,
    ],
  }).compile();
  const assetRepository = moduleRef.get(AssetRepository);

  return { assetRepository, entityRepositoryMock };
};
