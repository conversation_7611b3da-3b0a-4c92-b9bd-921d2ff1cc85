-- Performance Optimization Indexes
-- Run this script to add the recommended performance indexes

-- Enable timing for performance measurement
\timing on

-- Show current query performance BEFORE indexes
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) 
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Create the critical performance indexes
-- These indexes should dramatically improve query performance

-- 1. Primary index for alert_id lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excursion_stats_alert_id 
ON excursion_stats(alert_id);

-- 2. Time range index for date filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excursion_stats_time_range 
ON excursion_stats(start_time, end_time);

-- 3. Composite index for the exact query pattern used in getRangeGroupedStats
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excursion_stats_query_pattern 
ON excursion_stats(alert_id, start_time, end_time) 
INCLUDE (time_duration, avg_value, min_value, max_value);

-- 4. Customer-based alert filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_alerts_customer_id 
ON alerts(customer_id) WHERE deleted_at IS NULL;

-- 5. Asset and measurement lookup optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_alerts_asset_measurement 
ON alerts(asset_id, measurement_id) WHERE deleted_at IS NULL;

-- 6. Additional supporting indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_alerts_customer_enabled 
ON alerts(customer_id, enabled) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excursion_stats_start_time 
ON excursion_stats(start_time);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excursion_stats_end_time 
ON excursion_stats(end_time);

-- Update table statistics after index creation
ANALYZE excursion_stats;
ANALYZE alerts;
ANALYZE asset;
ANALYZE measurement;

-- Show query performance AFTER indexes
EXPLAIN (ANALYZE, BUFFERS) 
SELECT COUNT(*) 
FROM excursion_stats es
JOIN alerts a ON es.alert_id = a.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Test the actual getRangeGroupedStats query pattern
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    COUNT(*) as total_excursions,
    COALESCE(SUM(EXTRACT(EPOCH FROM es.time_duration)), 0) as total_duration_seconds
FROM excursion_stats es
INNER JOIN alerts a ON es.alert_id = a.id
INNER JOIN asset ast ON a.asset_id = ast.id  
INNER JOIN asset_type at ON ast.a_type = at.id
INNER JOIN measurement m ON a.measurement_id = m.id
INNER JOIN measurement_type mt ON m.m_type = mt.id
LEFT JOIN alert_threshold_type att ON a.threshold_type = att.id
WHERE a.customer_id = 1 
  AND es.start_time >= NOW() - interval '30 days'
  AND es.end_time <= NOW()
  AND a.deleted_at IS NULL;

-- Display index information
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('excursion_stats', 'alerts', 'asset', 'measurement')
ORDER BY tablename, indexname;

-- Display table sizes and index sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE tablename IN ('excursion_stats', 'alerts', 'asset', 'measurement')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
